{"indexes": [{"collectionGroup": "encrypted-files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userFingerprint", "order": "ASCENDING"}, {"fieldPath": "uploadedAt", "order": "DESCENDING"}]}, {"collectionGroup": "encrypted-files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userFingerprint", "order": "ASCENDING"}, {"fieldPath": "fileType", "order": "ASCENDING"}, {"fieldPath": "uploadedAt", "order": "DESCENDING"}]}, {"collectionGroup": "usage-stats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "action", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "usage-stats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "rating", "order": "DESCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "error-reports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "encrypted-files", "fieldPath": "userFingerprint", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "encrypted-files", "fieldPath": "uploadedAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "usage-stats", "fieldPath": "action", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "usage-stats", "fieldPath": "timestamp", "indexes": [{"order": "DESCENDING", "queryScope": "COLLECTION"}]}]}