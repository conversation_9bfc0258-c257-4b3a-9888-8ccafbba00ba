"""
تحويل الصور إلى خريطة بكسلات
"""

import json
from typing import List, Dict, Any, Optional
from PIL import Image
from tqdm import tqdm
from .utils import rgb_to_hex, save_json_compressed, get_image_info, create_output_directory


class ImageToMapConverter:
    """
    فئة تحويل الصور إلى خريطة بكسلات
    """
    
    def __init__(self):
        self.pixel_map = []
        self.metadata = {}
    
    def convert_image_to_map(self, image_path: str, output_path: str, 
                           compress_colors: bool = True, 
                           skip_transparent: bool = True) -> bool:
        """
        تحويل صورة إلى خريطة بكسلات
        
        Args:
            image_path: مسار الصورة الأصلية
            output_path: مسار ملف الإخراج
            compress_colors: ضغط الألوان المتشابهة
            skip_transparent: تجاهل البكسلات الشفافة
        """
        try:
            # فتح الصورة
            with Image.open(image_path) as img:
                # تحويل إلى RGBA للتعامل مع الشفافية
                if img.mode != 'RGBA':
                    img = img.convert('RGBA')
                
                pixels = img.load()
                width, height = img.size
                
                # إعداد البيانات الوصفية
                self.metadata = {
                    "original_file": image_path,
                    "width": width,
                    "height": height,
                    "total_pixels": width * height,
                    "format": img.format,
                    "mode": img.mode,
                    "version": "1.0"
                }
                
                # تحويل البكسلات
                self.pixel_map = []
                color_count = {}
                
                print(f"🔄 جاري تحويل الصورة ({width}x{height})...")
                
                for y in tqdm(range(height), desc="معالجة الصفوف"):
                    for x in range(width):
                        r, g, b, a = pixels[x, y]
                        
                        # تجاهل البكسلات الشفافة إذا كان مطلوباً
                        if skip_transparent and a == 0:
                            continue
                        
                        # تحويل اللون إلى hex
                        if a == 255:  # غير شفاف
                            hex_color = rgb_to_hex(r, g, b)
                        else:  # شفاف جزئياً
                            hex_color = f"#{r:02x}{g:02x}{b:02x}{a:02x}"
                        
                        # إضافة البكسل
                        pixel_data = {
                            "x": x,
                            "y": y,
                            "color": hex_color
                        }
                        
                        if a != 255:
                            pixel_data["alpha"] = a
                        
                        self.pixel_map.append(pixel_data)
                        
                        # عد الألوان للإحصائيات
                        color_count[hex_color] = color_count.get(hex_color, 0) + 1
                
                # ضغط الألوان المتشابهة
                if compress_colors:
                    self.pixel_map = self._compress_similar_colors(self.pixel_map)
                
                # إضافة إحصائيات
                self.metadata["processed_pixels"] = len(self.pixel_map)
                self.metadata["unique_colors"] = len(color_count)
                self.metadata["most_common_color"] = max(color_count, key=color_count.get) if color_count else None
                
                # حفظ الخريطة
                create_output_directory(output_path.rsplit('/', 1)[0] if '/' in output_path else '.')
                
                final_data = {
                    "metadata": self.metadata,
                    "pixels": self.pixel_map
                }
                
                save_json_compressed(final_data, output_path)
                
                print(f"✅ تم تحويل الصورة بنجاح!")
                print(f"   البكسلات المعالجة: {len(self.pixel_map):,}")
                print(f"   الألوان الفريدة: {len(color_count):,}")
                print(f"   ملف الإخراج: {output_path}")
                
                return True
                
        except Exception as e:
            print(f"❌ خطأ في تحويل الصورة: {str(e)}")
            return False
    
    def _compress_similar_colors(self, pixel_map: List[Dict]) -> List[Dict]:
        """
        ضغط الألوان المتشابهة (تحسين بسيط)
        """
        # هذه وظيفة بسيطة - يمكن تطويرها أكثر
        return pixel_map
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات التحويل
        """
        return {
            "metadata": self.metadata,
            "pixel_count": len(self.pixel_map)
        }
