"""
تحويل الصور إلى خريطة بكسلات
"""

import json
from typing import List, Dict, Any, Optional
from PIL import Image
from tqdm import tqdm
from .utils import rgb_to_hex, save_json_compressed, get_image_info, create_output_directory
from .compression import AdvancedPixelMapCompressor
from .encryption import PixelMapEncryption


class ImageToMapConverter:
    """
    فئة تحويل الصور إلى خريطة بكسلات
    """

    def __init__(self):
        self.pixel_map = []
        self.metadata = {}
        self.compressor = AdvancedPixelMapCompressor()
        self.encryption = PixelMapEncryption()

    def convert_image_to_map(self, image_path: str, output_path: str,
                           compress_colors: bool = False,
                           skip_transparent: bool = True,
                           compression_level: str = 'lossless',
                           encryption_key: str = None) -> bool:
        """
        تحويل صورة إلى خريطة بكسلات

        Args:
            image_path: مسار الصورة الأصلية
            output_path: مسار ملف الإخراج
            compress_colors: ضغط الألوان المتشابهة
            skip_transparent: تجاهل البكسلات الشفافة
        """
        try:
            # فتح الصورة
            with Image.open(image_path) as img:
                # تحويل إلى RGBA للتعامل مع الشفافية
                if img.mode != 'RGBA':
                    img = img.convert('RGBA')

                pixels = img.load()
                width, height = img.size

                # إعداد البيانات الوصفية
                self.metadata = {
                    "original_file": image_path,
                    "width": width,
                    "height": height,
                    "total_pixels": width * height,
                    "format": img.format,
                    "mode": img.mode,
                    "version": "1.0"
                }

                # تحويل البكسلات
                self.pixel_map = []
                color_count = {}

                print(f"🔄 جاري تحويل الصورة ({width}x{height})...")

                for y in tqdm(range(height), desc="معالجة الصفوف"):
                    for x in range(width):
                        r, g, b, a = pixels[x, y]

                        # تجاهل البكسلات الشفافة إذا كان مطلوباً
                        if skip_transparent and a == 0:
                            continue

                        # تحويل اللون إلى hex
                        if a == 255:  # غير شفاف
                            hex_color = rgb_to_hex(r, g, b)
                        else:  # شفاف جزئياً
                            hex_color = f"#{r:02x}{g:02x}{b:02x}{a:02x}"

                        # إضافة البكسل
                        pixel_data = {
                            "x": x,
                            "y": y,
                            "color": hex_color
                        }

                        if a != 255:
                            pixel_data["alpha"] = a

                        self.pixel_map.append(pixel_data)

                        # عد الألوان للإحصائيات
                        color_count[hex_color] = color_count.get(hex_color, 0) + 1

                # إضافة إحصائيات أولية
                self.metadata["processed_pixels"] = len(self.pixel_map)
                self.metadata["unique_colors"] = len(color_count)
                self.metadata["most_common_color"] = max(color_count, key=color_count.get) if color_count else None

                # حفظ البيانات بجودة 100% (بدون ضغط افتراضياً)
                if compress_colors and compression_level != 'lossless':
                    print(f"🗜️  تطبيق ضغط (مستوى: {compression_level})...")
                    print(f"⚠️  تحذير: قد يؤثر الضغط على الجودة!")

                    try:
                        # استخدام الضغط المتقدم
                        compressed_data = self.compressor.ultra_compress_pixel_map(
                            self.pixel_map, width, height, compression_level
                        )

                        # إضافة معلومات الضغط
                        self.metadata["compression"] = {
                            "enabled": True,
                            "method": compressed_data.get("compression_type", "unknown"),
                            "level": compression_level,
                            "original_pixels": len(self.pixel_map),
                            "compressed_size": len(str(compressed_data)),
                            "compression_ratio": compressed_data.get("metadata", {}).get("compression_ratio", 0)
                        }

                        final_data = {
                            "metadata": self.metadata,
                            "compressed_data": compressed_data,
                            "format_version": "2.0"
                        }

                        print(f"   📊 نسبة الضغط: {self.metadata['compression']['compression_ratio']:.1f}%")

                    except Exception as e:
                        print(f"⚠️  فشل الضغط: {e}")
                        print(f"🔄 التبديل إلى الحفظ بدون ضغط...")

                        # حفظ بدون ضغط في حالة فشل الضغط
                        final_data = {
                            "metadata": self.metadata,
                            "pixels": self.pixel_map,
                            "format_version": "1.0"
                        }
                else:
                    # حفظ بدون ضغط (جودة 100%)
                    print(f"💎 حفظ بجودة 100% (بدون ضغط)")
                    final_data = {
                        "metadata": self.metadata,
                        "pixels": self.pixel_map,
                        "format_version": "1.0"
                    }

                # تطبيق التشفير إذا تم توفير مفتاح
                if encryption_key:
                    print(f"🔐 تطبيق التشفير...")
                    try:
                        encrypted_data = self.encryption.encrypt_pixel_map(final_data, encryption_key)

                        # إضافة معلومات التشفير للإحصائيات
                        encryption_info = self.encryption.get_encryption_info(encrypted_data)
                        print(f"   🔒 خوارزمية التشفير: {encryption_info.get('algorithm', 'غير محدد')}")
                        print(f"   🔑 بصمة المفتاح: {encryption_info.get('key_length', 'غير محدد')} حرف")

                        final_data = encrypted_data

                    except Exception as e:
                        print(f"⚠️  فشل التشفير: {e}")
                        print(f"🔄 سيتم الحفظ بدون تشفير...")

                # حفظ الخريطة
                create_output_directory(output_path.rsplit('/', 1)[0] if '/' in output_path else '.')
                save_json_compressed(final_data, output_path)

                print(f"✅ تم تحويل الصورة بنجاح!")
                print(f"   البكسلات المعالجة: {len(self.pixel_map):,}")
                print(f"   الألوان الفريدة: {len(color_count):,}")
                print(f"   ملف الإخراج: {output_path}")

                return True

        except Exception as e:
            print(f"❌ خطأ في تحويل الصورة: {str(e)}")
            return False

    def _compress_similar_colors(self, pixel_map: List[Dict]) -> List[Dict]:
        """
        ضغط الألوان المتشابهة (تحسين بسيط)
        """
        # هذه وظيفة بسيطة - يمكن تطويرها أكثر
        return pixel_map

    def get_conversion_stats(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات التحويل
        """
        return {
            "metadata": self.metadata,
            "pixel_count": len(self.pixel_map)
        }
