// معالج الملفات الجديد - تخريب أسطر الملفات

// متغيرات عامة
let currentFile = null;
let currentFileName = '';
let currentFileContent = '';
let scrambledResult = null;
let rebuiltFile = null;

// معالج رفع الملف
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    const corruptedFileInput = document.getElementById('corruptedFileInput');
    const removedLinesInput = document.getElementById('removedLinesInput');
    
    if (fileInput) {
        fileInput.addEventListener('change', handleFileUpload);
    }
    
    if (corruptedFileInput) {
        corruptedFileInput.addEventListener('change', handleCorruptedFileUpload);
    }
    
    if (removedLinesInput) {
        removedLinesInput.addEventListener('change', handleRemovedLinesUpload);
    }
});

// رفع الملف للتخريب
async function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    console.log('📁 تم رفع ملف:', file.name);
    console.log('📊 حجم الملف:', (file.size / 1024).toFixed(2) + ' KB');
    console.log('🗂️ نوع الملف:', file.type || 'غير محدد');
    
    currentFile = file;
    currentFileName = file.name;
    
    try {
        // قراءة محتوى الملف كنص
        currentFileContent = await readFileAsText(file);
        console.log('✅ تم قراءة الملف بنجاح');
        console.log('📄 عدد الأسطر:', currentFileContent.split('\n').length);
        
        // تفعيل زر التخريب (لا يحتاج مفتاح)
        const scrambleBtn = document.getElementById('scrambleBtn');
        if (scrambleBtn) {
            scrambleBtn.disabled = false;
        }
        
        // عرض معلومات الملف
        displayFileInfo(file, currentFileContent);
        
    } catch (error) {
        console.error('❌ خطأ في قراءة الملف:', error);
        alert('فشل في قراءة الملف: ' + error.message);
    }
}

// قراءة الملف كنص
function readFileAsText(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            resolve(e.target.result);
        };
        
        reader.onerror = function(e) {
            reject(new Error('فشل في قراءة الملف'));
        };
        
        // محاولة قراءة الملف كنص UTF-8
        reader.readAsText(file, 'UTF-8');
    });
}

// عرض معلومات الملف
function displayFileInfo(file, content) {
    const lines = content.split('\n');
    const totalLines = lines.length;
    const linesToRemove = Math.min(50, Math.floor(totalLines * 0.2));
    
    console.log('📊 معلومات الملف:');
    console.log('  📁 الاسم:', file.name);
    console.log('  📏 الحجم:', (file.size / 1024).toFixed(2) + ' KB');
    console.log('  📄 إجمالي الأسطر:', totalLines);
    console.log('  ✂️ الأسطر المراد حذفها:', linesToRemove);
    console.log('  📊 النسبة المحذوفة:', ((linesToRemove / totalLines) * 100).toFixed(1) + '%');
    
    // يمكن إضافة عرض في الواجهة هنا
}

// تخريب الملف بدون مفتاح
function scrambleFile() {
    if (!currentFile || !currentFileContent) {
        alert('يرجى رفع ملف أولاً');
        return;
    }

    try {
        console.log('🔀 بدء تخريب الملف...');

        // تخريب الملف بدون مفتاح
        scrambledResult = scrambleFileWithoutKey(currentFileContent, currentFileName);

        console.log('✅ تم تخريب الملف بنجاح');

        // تفعيل أزرار التحميل
        const downloadCorruptedBtn = document.getElementById('downloadCorruptedBtn');
        const downloadRemovedBtn = document.getElementById('downloadRemovedBtn');

        if (downloadCorruptedBtn) downloadCorruptedBtn.disabled = false;
        if (downloadRemovedBtn) downloadRemovedBtn.disabled = false;

        // عرض النتائج
        displayScrambleResults();

    } catch (error) {
        console.error('❌ خطأ في تخريب الملف:', error);
        alert('فشل في تخريب الملف: ' + error.message);
    }
}

// تخريب الملف بدون مفتاح - وظيفة محلية
function scrambleFileWithoutKey(fileContent, fileName) {
    console.log('🔀 بدء تخريب أسطر الملف:', fileName);

    // تحويل الملف إلى أسطر
    const lines = fileContent.split('\n');
    const totalLines = lines.length;

    console.log('📊 إجمالي الأسطر:', totalLines);

    // تحديد عدد الأسطر المراد حذفها (20% من الملف أو 50 سطر كحد أقصى)
    const linesToRemove = Math.min(50, Math.floor(totalLines * 0.2));
    console.log('🗑️ الأسطر المراد حذفها:', linesToRemove);

    // إنشاء مصفوفة عشوائية للأسطر المراد حذفها
    const linesToRemoveIndices = generateRandomLinesWithoutKey(totalLines, linesToRemove);
    console.log('📋 فهارس الأسطر المحذوفة:', linesToRemoveIndices.slice(0, 10) + '...');

    // فصل الأسطر
    const removedLines = [];
    const remainingLines = [];

    for (let i = 0; i < lines.length; i++) {
        if (linesToRemoveIndices.includes(i)) {
            removedLines.push({
                index: i,
                content: lines[i]
            });
        } else {
            remainingLines.push(lines[i]);
        }
    }

    console.log('✅ تم تخريب الملف بنجاح');
    console.log('📄 الأسطر المتبقية:', remainingLines.length);
    console.log('🗑️ الأسطر المحذوفة:', removedLines.length);

    return {
        corruptedFile: remainingLines.join('\n'),
        removedLines: removedLines,
        originalFileName: fileName,
        totalLines: totalLines,
        removedCount: linesToRemove,
        linesToRemoveIndices: linesToRemoveIndices
    };
}

// إنشاء قائمة عشوائية بدون مفتاح
function generateRandomLinesWithoutKey(totalLines, linesToRemove) {
    console.log('🎲 إنشاء قائمة الأسطر العشوائية للحذف...');

    // إنشاء مصفوفة جميع أرقام الأسطر [0, 1, 2, 3, ...]
    const allLines = Array.from({length: totalLines}, (_, i) => i);

    // خلط المصفوفة عشوائياً (Fisher-Yates shuffle)
    for (let i = allLines.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [allLines[i], allLines[j]] = [allLines[j], allLines[i]];
    }

    // أخذ أول N عنصر كأسطر للحذف
    const linesToRemoveIndices = allLines.slice(0, linesToRemove).sort((a, b) => a - b);

    console.log('✅ تم إنشاء قائمة الأسطر للحذف:', linesToRemoveIndices.slice(0, 10) + '...');
    return linesToRemoveIndices;
}

// عرض نتائج التخريب
function displayScrambleResults() {
    if (!scrambledResult) return;

    console.log('📊 نتائج التخريب:');
    console.log('  📄 الأسطر المتبقية:', scrambledResult.corruptedFile.split('\n').length);
    console.log('  🗑️ الأسطر المحذوفة:', scrambledResult.removedLines.length);
    console.log('  📊 إجمالي الأسطر الأصلية:', scrambledResult.totalLines);

    // يمكن إضافة عرض في الواجهة هنا
}

// تحميل الملف المخرب
function downloadCorruptedFile() {
    if (!scrambledResult) {
        alert('لا يوجد ملف مخرب للتحميل');
        return;
    }
    
    const fileName = `corrupted_${scrambledResult.originalFileName}`;
    downloadTextFile(scrambledResult.corruptedFile, fileName);
    
    console.log('💾 تم تحميل الملف المخرب:', fileName);
}

// تحميل ملف الأسطر المحذوفة مع معلومات الترتيب
function downloadRemovedLines() {
    if (!scrambledResult) {
        alert('لا يوجد ملف أسطر محذوفة للتحميل');
        return;
    }

    const removedData = {
        originalFileName: scrambledResult.originalFileName,
        totalLines: scrambledResult.totalLines,
        removedCount: scrambledResult.removedCount,
        removedLines: scrambledResult.removedLines,
        linesToRemoveIndices: scrambledResult.linesToRemoveIndices,
        orderingInfo: {
            description: "معلومات ترتيب الأسطر لإعادة البناء",
            removedPositions: scrambledResult.linesToRemoveIndices,
            totalOriginalLines: scrambledResult.totalLines
        },
        metadata: {
            created_at: new Date().toISOString(),
            version: "2.0",
            description: "ملف الأسطر المحذوفة مع معلومات الترتيب - مطلوب لإعادة بناء الملف الأصلي",
            noKeyRequired: true
        }
    };

    const fileName = `removed_lines_${scrambledResult.originalFileName}.json`;
    downloadTextFile(JSON.stringify(removedData, null, 2), fileName);

    console.log('💾 تم تحميل ملف الأسطر المحذوفة:', fileName);
}

// تحميل ملف نصي
function downloadTextFile(content, fileName) {
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
}

// متغيرات إعادة البناء
let corruptedFileContent = '';
let removedLinesContent = '';

// رفع الملف المخرب
async function handleCorruptedFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
        corruptedFileContent = await readFileAsText(file);
        console.log('📁 تم رفع الملف المخرب:', file.name);
        checkRebuildReady();
    } catch (error) {
        console.error('❌ خطأ في قراءة الملف المخرب:', error);
        alert('فشل في قراءة الملف المخرب');
    }
}

// رفع ملف الأسطر المحذوفة
async function handleRemovedLinesUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
        removedLinesContent = await readFileAsText(file);
        console.log('📄 تم رفع ملف الأسطر المحذوفة:', file.name);
        checkRebuildReady();
    } catch (error) {
        console.error('❌ خطأ في قراءة ملف الأسطر المحذوفة:', error);
        alert('فشل في قراءة ملف الأسطر المحذوفة');
    }
}

// التحقق من جاهزية إعادة البناء (بدون مفتاح)
function checkRebuildReady() {
    const rebuildBtn = document.getElementById('rebuildBtn');

    if (corruptedFileContent && removedLinesContent && rebuildBtn) {
        rebuildBtn.disabled = false;
        console.log('✅ جاهز لإعادة البناء');
    }
}

// إعادة بناء الملف بدون مفتاح
function rebuildFile() {
    if (!corruptedFileContent || !removedLinesContent) {
        alert('يرجى رفع الملفين أولاً');
        return;
    }

    try {
        console.log('🔄 بدء إعادة بناء الملف...');

        // إعادة بناء الملف بدون مفتاح
        rebuiltFile = rebuildFileWithoutKey(corruptedFileContent, removedLinesContent);

        console.log('✅ تم إعادة بناء الملف بنجاح');
        console.log('📄 إجمالي الأسطر المستعادة:', rebuiltFile.totalLines);
        console.log('🔄 الأسطر المستعادة:', rebuiltFile.restoredLines);

        // تفعيل زر التحميل
        const downloadRebuiltBtn = document.getElementById('downloadRebuiltBtn');
        if (downloadRebuiltBtn) {
            downloadRebuiltBtn.disabled = false;
        }

    } catch (error) {
        console.error('❌ خطأ في إعادة البناء:', error);
        alert('فشل في إعادة بناء الملف: ' + error.message);
    }
}

// إعادة بناء الملف من الملفين بدون مفتاح
function rebuildFileWithoutKey(corruptedFile, removedLinesFile) {
    console.log('🔄 بدء إعادة بناء الملف...');

    try {
        // قراءة الملف المخرب
        const corruptedLines = corruptedFile.split('\n');

        // قراءة ملف الأسطر المحذوفة (JSON)
        const removedData = JSON.parse(removedLinesFile);

        console.log('📊 الأسطر المخربة:', corruptedLines.length);
        console.log('🗑️ الأسطر المحذوفة:', removedData.removedLines.length);

        // إعادة بناء الملف الأصلي
        const rebuiltLines = new Array(removedData.totalLines);

        // وضع الأسطر المحذوفة في مكانها الصحيح
        removedData.removedLines.forEach(line => {
            rebuiltLines[line.index] = line.content;
        });

        // ملء الأماكن الفارغة بالأسطر المتبقية
        let corruptedIndex = 0;
        for (let i = 0; i < rebuiltLines.length; i++) {
            if (rebuiltLines[i] === undefined) {
                rebuiltLines[i] = corruptedLines[corruptedIndex] || '';
                corruptedIndex++;
            }
        }

        console.log('✅ تم إعادة بناء الملف بنجاح');
        console.log('📄 إجمالي الأسطر المستعادة:', rebuiltLines.length);

        return {
            rebuiltContent: rebuiltLines.join('\n'),
            originalFileName: removedData.originalFileName,
            totalLines: removedData.totalLines,
            restoredLines: removedData.removedLines.length
        };

    } catch (error) {
        console.error('❌ خطأ في إعادة البناء:', error);
        throw new Error(`فشل في إعادة بناء الملف: ${error.message}`);
    }
}

// تحميل الملف المستعاد
function downloadRebuiltFile() {
    if (!rebuiltFile) {
        alert('لا يوجد ملف مستعاد للتحميل');
        return;
    }

    const fileName = `rebuilt_${rebuiltFile.originalFileName}`;
    downloadTextFile(rebuiltFile.rebuiltContent, fileName);

    console.log('💾 تم تحميل الملف المستعاد:', fileName);
}
