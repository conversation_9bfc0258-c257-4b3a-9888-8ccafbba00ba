# 🚀 كيفية تشغيل Pixel Map Converter

## 📋 الملخص السريع

لتشغيل التطبيق على Windows، تحتاج إلى:
1. **تثبيت Python** (إذا لم يكن مثبت)
2. **تشغيل التطبيق** باستخدام أحد الملفات المتوفرة

## 🐍 الخطوة 1: تثبيت Python

### إذا لم يكن Python مثبت:

1. **اذهب إلى**: https://www.python.org/downloads/
2. **اضغط "Download Python 3.x.x"** (أحدث إصدار)
3. **شغل الملف المُحمل**
4. **✅ تأكد من تحديد "Add Python to PATH"** (مهم جداً!)
5. **اضغط "Install Now"**
6. **انتظر اكتمال التثبيت**

### للتحقق من التثبيت:
```cmd
# افتح Command Prompt (اضغط Win+R، اكتب cmd، اضغط Enter)
python --version
```

## 🎨 الخطوة 2: تشغيل التطبيق

### الطريقة 1: العرض التوضيحي المبسط (الأسهل)

**انقر نقراً مزدوجاً على:**
```
start_demo.bat
```

**أو افتح Command Prompt واكتب:**
```cmd
cd c:\Users\<USER>\Desktop\python
start_demo.bat
```

### الطريقة 2: التطبيق الكامل

**انقر نقراً مزدوجاً على:**
```
run_app.bat
```

### الطريقة 3: تشغيل مباشر

```cmd
# افتح Command Prompt في مجلد المشروع
cd c:\Users\<USER>\Desktop\python

# للعرض التوضيحي
python demo_app_simple.py

# للتطبيق الكامل (بعد تثبيت المكتبات)
python -m pip install -r requirements.txt
python main.py --help
```

## 📱 الخطوة 3: تشغيل تطبيق الأندرويد (اختياري)

```cmd
# تثبيت مكتبات Kivy
python -m pip install kivy kivymd plyer

# تشغيل تطبيق الأندرويد على سطح المكتب
python android_app.py
```

## 🎯 ما ستراه عند التشغيل

### العرض التوضيحي المبسط:
```
┌─────────────────────────────────────┐
│        Pixel Map Converter          │
├─────────────────────────────────────┤
│  📷 تحويل صورة إلى خريطة بكسلات     │
│  [📁 اختيار صورة] [🔄 تحويل]       │
│                                     │
│  🗺️ تحويل خريطة إلى صورة            │
│  [📁 اختيار خريطة] [🖼️ إعادة بناء]   │
│                                     │
│  ℹ️ معلومات ونتائج                  │
│  [منطقة النصوص والمعلومات]          │
└─────────────────────────────────────┘
```

### التطبيق الكامل:
```
╔══════════════════════════════════════╗
║        Pixel Map Converter           ║
╠══════════════════════════════════════╣
║ 1. تحويل صورة إلى خريطة بكسلات      ║
║ 2. تحويل خريطة بكسلات إلى صورة      ║
║ 3. عرض معلومات ملف                 ║
║ 4. اختبار شامل للتطبيق             ║
║ 5. عرض المساعدة                    ║
║ 6. خروج                            ║
╚══════════════════════════════════════╝
```

## 📁 ملفات الإخراج

ستجد النتائج في:
```
c:\Users\<USER>\Desktop\python\output\
├── image_name_pixel_map.json    # خريطة البكسلات
├── image_name_restored.png      # الصورة المُعادة
└── ...
```

## 🔧 حل المشاكل الشائعة

### مشكلة: "python is not recognized"
**الحل:**
1. أعد تثبيت Python مع تحديد "Add Python to PATH"
2. أعد تشغيل Command Prompt
3. جرب `py` بدلاً من `python`

### مشكلة: "No module named 'PIL'"
**الحل:**
```cmd
python -m pip install Pillow
```

### مشكلة: "No module named 'tkinter'"
**الحل:**
- أعد تثبيت Python مع تحديد جميع المكونات

### مشكلة: التطبيق لا يفتح
**الحل:**
1. تأكد من وجود ملف `demo_app_simple.py`
2. تأكد من تثبيت Python بشكل صحيح
3. جرب تشغيل الأمر من Command Prompt

## 📖 ملفات مفيدة للمراجعة

- **`README.md`** - الدليل الشامل للتطبيق
- **`QUICK_START.md`** - دليل البدء السريع
- **`INSTALL_PYTHON.md`** - دليل تثبيت Python المفصل
- **`APK_BUILD_GUIDE.md`** - دليل بناء تطبيق الأندرويد

## 🎉 استمتع بالتطبيق!

بعد التشغيل الناجح، يمكنك:
- ✅ اختيار صور وتحويلها إلى خريطة بكسلات
- ✅ إعادة بناء الصور من الخرائط
- ✅ مشاهدة المعلومات والإحصائيات
- ✅ حفظ النتائج في مجلد منظم

---

**استمتع بتحويل صورك إلى خرائط بكسلات! 🎨✨**
