@echo off
chcp 65001 >nul
title Pixel Map Converter

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎨 Pixel Map Converter 🎨                 ║
echo ║                  تطبيق تحويل الصور إلى خريطة بكسلات          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير مضاف إلى PATH
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    echo تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM التحقق من المتطلبات
echo 🔍 التحقق من المتطلبات...
python -c "import PIL, click, colorama, tqdm, numpy" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  بعض المتطلبات مفقودة. جاري التثبيت...
    python -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المتطلبات متوفرة
echo.

REM إنشاء صور تجريبية إذا لم تكن موجودة
if not exist "examples\simple_10x10.png" (
    echo 🎨 إنشاء صور تجريبية...
    python create_test_image.py
    echo.
)

REM عرض القائمة الرئيسية
:menu
echo ═══════════════════════════════════════════════════════════════
echo                           القائمة الرئيسية
echo ═══════════════════════════════════════════════════════════════
echo.
echo 1. تحويل صورة إلى خريطة بكسلات
echo 2. تحويل خريطة بكسلات إلى صورة  
echo 3. عرض معلومات ملف
echo 4. اختبار شامل للتطبيق
echo 5. عرض المساعدة
echo 6. خروج
echo.
set /p choice="اختر رقماً (1-6): "

if "%choice%"=="1" goto image_to_map
if "%choice%"=="2" goto map_to_image
if "%choice%"=="3" goto show_info
if "%choice%"=="4" goto run_tests
if "%choice%"=="5" goto show_help
if "%choice%"=="6" goto exit
echo ❌ اختيار غير صحيح
goto menu

:image_to_map
echo.
echo 🔄 تحويل صورة إلى خريطة بكسلات
echo ═══════════════════════════════════════
echo.
echo الصور المتاحة في مجلد examples:
dir /b examples\*.png 2>nul
echo.
set /p image_file="أدخل اسم ملف الصورة (مع المسار): "
if "%image_file%"=="" set image_file=examples\simple_10x10.png

echo.
echo جاري التحويل...
python main.py image-to-map "%image_file%"
echo.
pause
goto menu

:map_to_image
echo.
echo 🔄 تحويل خريطة بكسلات إلى صورة
echo ═══════════════════════════════════════
echo.
echo ملفات الخرائط المتاحة في مجلد output:
dir /b output\*_pixel_map.json 2>nul
echo.
set /p map_file="أدخل اسم ملف الخريطة (مع المسار): "
if "%map_file%"=="" (
    for %%f in (output\*_pixel_map.json) do (
        set map_file=%%f
        goto found_map
    )
    echo ❌ لا توجد ملفات خرائط. قم بتحويل صورة أولاً.
    pause
    goto menu
)

:found_map
echo.
echo جاري التحويل...
python main.py map-to-image "%map_file%"
echo.
pause
goto menu

:show_info
echo.
echo ℹ️  عرض معلومات ملف
echo ═══════════════════════════════════════
echo.
set /p info_file="أدخل مسار الملف: "
if "%info_file%"=="" set info_file=examples\simple_10x10.png

echo.
python main.py info "%info_file%"
echo.
pause
goto menu

:run_tests
echo.
echo 🧪 تشغيل اختبار شامل
echo ═══════════════════════════════════════
echo.
python test_app.py
echo.
pause
goto menu

:show_help
echo.
echo 📖 المساعدة
echo ═══════════════════════════════════════
echo.
python main.py --help
echo.
echo للمزيد من المعلومات، راجع ملف README.md
echo.
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام Pixel Map Converter!
echo.
pause
exit /b 0
