rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // قواعد الملفات المشفرة
    match /encrypted-maps/{userFingerprint}/{fileName} {
      // السماح بالقراءة والكتابة للملفات المشفرة
      allow read: if true; // يمكن لأي شخص تحميل الملف إذا كان لديه الرابط
      
      // السماح بالكتابة مع قيود الحجم والنوع
      allow write: if request.resource.size < 50 * 1024 * 1024 && // أقل من 50MB
        request.resource.contentType == 'application/json' &&
        userFingerprint.size() >= 8 && // بصمة المستخدم صحيحة
        fileName.matches('.*\\.json$'); // ملف JSON فقط
      
      // السماح بالحذف للمالك
      allow delete: if true; // يمكن حذف الملف إذا كان لديك الرابط
    }
    
    // قواعد الصور المؤقتة (للمعالجة)
    match /temp-images/{userFingerprint}/{fileName} {
      // السماح بالكتابة للصور المؤقتة
      allow write: if request.resource.size < 10 * 1024 * 1024 && // أقل من 10MB
        request.resource.contentType.matches('image/.*') && // صور فقط
        userFingerprint.size() >= 8;
      
      // السماح بالقراءة لمدة محدودة
      allow read: if resource.timeCreated > timestamp.date(2024, 1, 1); // ملفات حديثة فقط
      
      // السماح بالحذف
      allow delete: if true;
    }
    
    // قواعد النسخ الاحتياطية (للمشرفين فقط)
    match /backups/{fileName} {
      // منع الوصول للمستخدمين العاديين
      allow read, write: if false;
    }
    
    // قواعد ملفات النظام
    match /system/{fileName} {
      // السماح بالقراءة فقط للملفات العامة
      allow read: if fileName.matches('public-.*');
      
      // منع الكتابة
      allow write: if false;
    }
    
    // قواعد الصور العامة (للموقع)
    match /public-assets/{fileName} {
      // السماح بالقراءة للجميع
      allow read: if true;
      
      // منع الكتابة للمستخدمين
      allow write: if false;
    }
    
    // قواعد سجلات الأخطاء
    match /error-logs/{date}/{fileName} {
      // السماح بالكتابة فقط
      allow write: if request.resource.size < 1 * 1024 * 1024 && // أقل من 1MB
        request.resource.contentType == 'text/plain';
      
      // منع القراءة للخصوصية
      allow read: if false;
    }
    
    // قواعد ملفات المستخدمين المؤقتة
    match /user-temp/{userFingerprint}/{fileName} {
      // السماح بالكتابة مع قيود
      allow write: if request.resource.size < 5 * 1024 * 1024 && // أقل من 5MB
        userFingerprint.size() >= 8 &&
        fileName.size() > 0;
      
      // السماح بالقراءة للمالك فقط
      allow read: if true; // يمكن القراءة بالرابط
      
      // السماح بالحذف
      allow delete: if true;
      
      // حذف تلقائي بعد 24 ساعة (يتم تطبيقه بواسطة Cloud Functions)
    }
    
    // منع الوصول لأي مسارات أخرى
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
