"""
تحويل خريطة البكسلات إلى صورة
"""

from typing import Dict, Any, Tu<PERSON>, List
from PIL import Image
from tqdm import tqdm
from .utils import hex_to_rgb, load_json, create_output_directory
from .compression import AdvancedPixelMapCompressor
from .encryption import PixelMapEncryption


class MapToImageConverter:
    """
    فئة تحويل خريطة البكسلات إلى صورة
    """

    def __init__(self):
        self.metadata = {}
        self.pixel_data = []
        self.compressor = AdvancedPixelMapCompressor()
        self.encryption = PixelMapEncryption()

    def convert_map_to_image(self, map_path: str, output_path: str,
                           background_color: Tuple[int, int, int, int] = (255, 255, 255, 255),
                           encryption_key: str = None) -> bool:
        """
        تحويل خريطة بكسلات إلى صورة

        Args:
            map_path: مسار ملف الخريطة
            output_path: مسار الصورة الناتجة
            background_color: لون الخلفية (R, G, B, A)
        """
        try:
            # تحميل بيانات الخريطة
            print(f"📂 جاري تحميل خريطة البكسلات...")
            map_data = load_json(map_path)

            # التحقق من التشفير
            if self.encryption.is_encrypted(map_data):
                print(f"🔐 اكتشاف ملف مشفر...")

                if not encryption_key:
                    print("❌ الملف مشفر ولكن لم يتم توفير مفتاح فك التشفير!")
                    return False

                # التحقق من صحة المفتاح
                if not self.encryption.verify_key(map_data, encryption_key):
                    print("❌ مفتاح فك التشفير غير صحيح!")
                    return False

                print(f"🔓 جاري فك التشفير...")
                try:
                    map_data = self.encryption.decrypt_pixel_map(map_data, encryption_key)
                    print(f"✅ تم فك التشفير بنجاح!")
                except Exception as e:
                    print(f"❌ فشل في فك التشفير: {e}")
                    return False

            # التحقق من إصدار التنسيق
            format_version = map_data.get("format_version", "1.0")

            if format_version == "2.0" and "compressed_data" in map_data:
                # تنسيق مضغوط جديد
                print(f"🗜️  اكتشاف تنسيق مضغوط - جاري إلغاء الضغط...")

                self.metadata = map_data.get("metadata", {})
                compressed_data = map_data["compressed_data"]

                # إلغاء ضغط البيانات
                self.pixel_data = self._decompress_pixel_data(compressed_data)

                compression_info = self.metadata.get("compression", {})
                print(f"   📊 طريقة الضغط: {compression_info.get('method', 'غير محدد')}")
                print(f"   📊 نسبة الضغط: {compression_info.get('compression_ratio', 0):.1f}%")

            else:
                # تنسيق قديم غير مضغوط
                self.metadata = map_data.get("metadata", {})
                self.pixel_data = map_data.get("pixels", [])

            if not self.pixel_data:
                print("❌ لا توجد بيانات بكسلات في الملف!")
                return False

            # الحصول على أبعاد الصورة
            width = self.metadata.get("width")
            height = self.metadata.get("height")

            if not width or not height:
                # حساب الأبعاد من البيانات
                max_x = max(p["x"] for p in self.pixel_data)
                max_y = max(p["y"] for p in self.pixel_data)
                width = max_x + 1
                height = max_y + 1
                print(f"⚠️  تم حساب الأبعاد من البيانات: {width}x{height}")

            print(f"🖼️  إنشاء صورة بحجم {width}x{height}...")

            # إنشاء صورة جديدة
            img = Image.new("RGBA", (width, height), background_color)
            pixels = img.load()

            # ملء البكسلات
            print(f"🎨 جاري رسم {len(self.pixel_data):,} بكسل...")

            for pixel in tqdm(self.pixel_data, desc="رسم البكسلات"):
                x = pixel["x"]
                y = pixel["y"]
                color_hex = pixel["color"]

                # التحقق من صحة الإحداثيات
                if 0 <= x < width and 0 <= y < height:
                    try:
                        # تحويل اللون
                        if len(color_hex) == 7:  # RGB فقط
                            r, g, b = hex_to_rgb(color_hex)
                            alpha = pixel.get("alpha", 255)
                            pixels[x, y] = (r, g, b, alpha)
                        elif len(color_hex) == 9:  # RGBA
                            color_hex_rgb = color_hex[:7]
                            r, g, b = hex_to_rgb(color_hex_rgb)
                            alpha = int(color_hex[7:9], 16)
                            pixels[x, y] = (r, g, b, alpha)
                        else:
                            print(f"⚠️  تنسيق لون غير صحيح: {color_hex}")
                            continue

                    except ValueError as e:
                        print(f"⚠️  خطأ في تحويل اللون {color_hex}: {e}")
                        continue
                else:
                    print(f"⚠️  إحداثيات خارج النطاق: ({x}, {y})")

            # حفظ الصورة
            create_output_directory(output_path.rsplit('/', 1)[0] if '/' in output_path else '.')

            # تحديد تنسيق الحفظ
            if output_path.lower().endswith('.png'):
                img.save(output_path, "PNG")
            elif output_path.lower().endswith(('.jpg', '.jpeg')):
                # تحويل إلى RGB للـ JPEG
                rgb_img = Image.new("RGB", (width, height), background_color[:3])
                rgb_img.paste(img, mask=img.split()[-1])  # استخدام قناة الألفا كقناع
                rgb_img.save(output_path, "JPEG", quality=95)
            else:
                # افتراضي PNG
                img.save(output_path, "PNG")

            print(f"✅ تم إنشاء الصورة بنجاح!")
            print(f"   الأبعاد: {width}x{height}")
            print(f"   البكسلات المرسومة: {len(self.pixel_data):,}")
            print(f"   ملف الإخراج: {output_path}")

            return True

        except Exception as e:
            print(f"❌ خطأ في تحويل الخريطة: {str(e)}")
            return False

    def _decompress_pixel_data(self, compressed_data: Dict) -> List[Dict]:
        """إلغاء ضغط بيانات البكسلات"""
        try:
            compression_type = compressed_data.get("compression_type", "unknown")
            print(f"🔍 نوع الضغط: {compression_type}")

            if compression_type == "palette":
                return self._decompress_palette_data(compressed_data)
            elif compression_type == "smart_regions":
                return self._decompress_regions_data(compressed_data)
            elif compression_type == "hybrid":
                return self._decompress_hybrid_data(compressed_data)
            elif compression_type == "rle":
                return self._decompress_rle_data(compressed_data)
            elif compression_type == "basic":
                return self._decompress_basic_data(compressed_data)
            else:
                print(f"⚠️  نوع ضغط غير مدعوم: {compression_type}")
                # محاولة استخراج البيانات مباشرة
                if "pixels" in compressed_data:
                    return compressed_data["pixels"]
                elif "data" in compressed_data:
                    return compressed_data["data"]
                else:
                    return []

        except Exception as e:
            print(f"⚠️  خطأ في إلغاء الضغط: {e}")
            print(f"🔄 محاولة استخراج البيانات مباشرة...")

            # محاولة أخيرة لاستخراج البيانات
            try:
                if isinstance(compressed_data, dict):
                    if "pixels" in compressed_data:
                        return compressed_data["pixels"]
                    elif "data" in compressed_data:
                        return compressed_data["data"]
                return []
            except:
                return []

    def _decompress_palette_data(self, compressed_data: Dict) -> List[Dict]:
        """إلغاء ضغط بيانات لوحة الألوان"""
        palette = compressed_data.get("palette", [])
        data = compressed_data.get("data", [])
        metadata = compressed_data.get("metadata", {})

        width = metadata.get("width", 0)
        height = metadata.get("height", 0)

        pixels = []

        for y, row_data in enumerate(data):
            x = 0
            for item in row_data:
                if isinstance(item, list):
                    # [color_index, count]
                    color_index, count = item
                    color = palette[color_index] if color_index < len(palette) else "#000000"

                    for _ in range(count):
                        if x < width:
                            pixels.append({"x": x, "y": y, "color": color})
                            x += 1
                else:
                    # color_index واحد
                    color_index = item
                    color = palette[color_index] if color_index < len(palette) else "#000000"
                    pixels.append({"x": x, "y": y, "color": color})
                    x += 1

        return pixels

    def _decompress_regions_data(self, compressed_data: Dict) -> List[Dict]:
        """إلغاء ضغط بيانات المناطق"""
        regions = compressed_data.get("regions", [])
        pixels = []

        for region in regions:
            if region.get("type") == "rect":
                # منطقة مستطيلة
                x, y = region["x"], region["y"]
                width, height = region["width"], region["height"]
                color = region["color"]

                for dy in range(height):
                    for dx in range(width):
                        pixels.append({
                            "x": x + dx,
                            "y": y + dy,
                            "color": color
                        })
            else:
                # بكسل واحد
                pixels.append({
                    "x": region["x"],
                    "y": region["y"],
                    "color": region["color"]
                })

        return pixels

    def _decompress_hybrid_data(self, compressed_data: Dict) -> List[Dict]:
        """إلغاء ضغط البيانات الهجينة"""
        regions = compressed_data.get("regions", [])
        all_pixels = []

        for region_data in regions:
            # إلغاء ضغط كل منطقة حسب نوعها
            region_pixels = self._decompress_pixel_data(region_data)

            # تحويل الإحداثيات إلى مطلقة
            start_x = region_data.get("start_x", 0)
            start_y = region_data.get("start_y", 0)

            for pixel in region_pixels:
                all_pixels.append({
                    "x": pixel["x"] + start_x,
                    "y": pixel["y"] + start_y,
                    "color": pixel["color"]
                })

        return all_pixels

    def _decompress_rle_data(self, compressed_data: Dict) -> List[Dict]:
        """إلغاء ضغط بيانات RLE"""
        data = compressed_data.get("data", [])
        pixels = []

        for item in data:
            x = item.get("x", 0)
            y = item.get("y", 0)
            color = item.get("color", "#000000")
            length = item.get("length", 1)

            for i in range(length):
                pixels.append({
                    "x": x + i,
                    "y": y,
                    "color": color
                })

        return pixels

    def _decompress_basic_data(self, compressed_data: Dict) -> List[Dict]:
        """إلغاء ضغط البيانات الأساسية"""
        data = compressed_data.get("data", [])
        pixels = []

        for color_group in data:
            color = color_group.get("color", "#000000")
            positions = color_group.get("positions", [])

            for pos in positions:
                pixels.append({
                    "x": pos.get("x", 0),
                    "y": pos.get("y", 0),
                    "color": color
                })

        return pixels

    def get_map_info(self, map_path: str) -> Dict[str, Any]:
        """
        الحصول على معلومات خريطة البكسلات
        """
        try:
            map_data = load_json(map_path)
            metadata = map_data.get("metadata", {})
            pixels = map_data.get("pixels", [])

            return {
                "metadata": metadata,
                "pixel_count": len(pixels),
                "file_size": len(str(map_data)),
                "dimensions": f"{metadata.get('width', 'غير محدد')}x{metadata.get('height', 'غير محدد')}",
                "unique_colors": metadata.get("unique_colors", "غير محدد")
            }
        except Exception as e:
            return {"error": str(e)}

    def validate_map_file(self, map_path: str) -> bool:
        """
        التحقق من صحة ملف الخريطة
        """
        try:
            map_data = load_json(map_path)

            # التحقق من وجود البيانات المطلوبة
            if "pixels" not in map_data:
                return False

            pixels = map_data["pixels"]
            if not isinstance(pixels, list) or len(pixels) == 0:
                return False

            # التحقق من تنسيق البكسل الأول
            first_pixel = pixels[0]
            required_keys = ["x", "y", "color"]

            return all(key in first_pixel for key in required_keys)

        except Exception:
            return False
