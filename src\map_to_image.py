"""
تحويل خريطة البكسلات إلى صورة
"""

from typing import Dict, Any, Tuple
from PIL import Image
from tqdm import tqdm
from .utils import hex_to_rgb, load_json, create_output_directory


class MapToImageConverter:
    """
    فئة تحويل خريطة البكسلات إلى صورة
    """
    
    def __init__(self):
        self.metadata = {}
        self.pixel_data = []
    
    def convert_map_to_image(self, map_path: str, output_path: str, 
                           background_color: Tuple[int, int, int, int] = (255, 255, 255, 255)) -> bool:
        """
        تحويل خريطة بكسلات إلى صورة
        
        Args:
            map_path: مسار ملف الخريطة
            output_path: مسار الصورة الناتجة
            background_color: لون الخلفية (R, G, B, A)
        """
        try:
            # تحميل بيانات الخريطة
            print(f"📂 جاري تحميل خريطة البكسلات...")
            map_data = load_json(map_path)
            
            # استخراج البيانات الوصفية والبكسلات
            self.metadata = map_data.get("metadata", {})
            self.pixel_data = map_data.get("pixels", [])
            
            if not self.pixel_data:
                print("❌ لا توجد بيانات بكسلات في الملف!")
                return False
            
            # الحصول على أبعاد الصورة
            width = self.metadata.get("width")
            height = self.metadata.get("height")
            
            if not width or not height:
                # حساب الأبعاد من البيانات
                max_x = max(p["x"] for p in self.pixel_data)
                max_y = max(p["y"] for p in self.pixel_data)
                width = max_x + 1
                height = max_y + 1
                print(f"⚠️  تم حساب الأبعاد من البيانات: {width}x{height}")
            
            print(f"🖼️  إنشاء صورة بحجم {width}x{height}...")
            
            # إنشاء صورة جديدة
            img = Image.new("RGBA", (width, height), background_color)
            pixels = img.load()
            
            # ملء البكسلات
            print(f"🎨 جاري رسم {len(self.pixel_data):,} بكسل...")
            
            for pixel in tqdm(self.pixel_data, desc="رسم البكسلات"):
                x = pixel["x"]
                y = pixel["y"]
                color_hex = pixel["color"]
                
                # التحقق من صحة الإحداثيات
                if 0 <= x < width and 0 <= y < height:
                    try:
                        # تحويل اللون
                        if len(color_hex) == 7:  # RGB فقط
                            r, g, b = hex_to_rgb(color_hex)
                            alpha = pixel.get("alpha", 255)
                            pixels[x, y] = (r, g, b, alpha)
                        elif len(color_hex) == 9:  # RGBA
                            color_hex_rgb = color_hex[:7]
                            r, g, b = hex_to_rgb(color_hex_rgb)
                            alpha = int(color_hex[7:9], 16)
                            pixels[x, y] = (r, g, b, alpha)
                        else:
                            print(f"⚠️  تنسيق لون غير صحيح: {color_hex}")
                            continue
                            
                    except ValueError as e:
                        print(f"⚠️  خطأ في تحويل اللون {color_hex}: {e}")
                        continue
                else:
                    print(f"⚠️  إحداثيات خارج النطاق: ({x}, {y})")
            
            # حفظ الصورة
            create_output_directory(output_path.rsplit('/', 1)[0] if '/' in output_path else '.')
            
            # تحديد تنسيق الحفظ
            if output_path.lower().endswith('.png'):
                img.save(output_path, "PNG")
            elif output_path.lower().endswith(('.jpg', '.jpeg')):
                # تحويل إلى RGB للـ JPEG
                rgb_img = Image.new("RGB", (width, height), background_color[:3])
                rgb_img.paste(img, mask=img.split()[-1])  # استخدام قناة الألفا كقناع
                rgb_img.save(output_path, "JPEG", quality=95)
            else:
                # افتراضي PNG
                img.save(output_path, "PNG")
            
            print(f"✅ تم إنشاء الصورة بنجاح!")
            print(f"   الأبعاد: {width}x{height}")
            print(f"   البكسلات المرسومة: {len(self.pixel_data):,}")
            print(f"   ملف الإخراج: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحويل الخريطة: {str(e)}")
            return False
    
    def get_map_info(self, map_path: str) -> Dict[str, Any]:
        """
        الحصول على معلومات خريطة البكسلات
        """
        try:
            map_data = load_json(map_path)
            metadata = map_data.get("metadata", {})
            pixels = map_data.get("pixels", [])
            
            return {
                "metadata": metadata,
                "pixel_count": len(pixels),
                "file_size": len(str(map_data)),
                "dimensions": f"{metadata.get('width', 'غير محدد')}x{metadata.get('height', 'غير محدد')}",
                "unique_colors": metadata.get("unique_colors", "غير محدد")
            }
        except Exception as e:
            return {"error": str(e)}
    
    def validate_map_file(self, map_path: str) -> bool:
        """
        التحقق من صحة ملف الخريطة
        """
        try:
            map_data = load_json(map_path)
            
            # التحقق من وجود البيانات المطلوبة
            if "pixels" not in map_data:
                return False
            
            pixels = map_data["pixels"]
            if not isinstance(pixels, list) or len(pixels) == 0:
                return False
            
            # التحقق من تنسيق البكسل الأول
            first_pixel = pixels[0]
            required_keys = ["x", "y", "color"]
            
            return all(key in first_pixel for key in required_keys)
            
        except Exception:
            return False
