@echo off
chcp 65001 >nul
title Pixel Map Converter - Demo

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎨 Pixel Map Converter 🎨                 ║
echo ║                     عرض توضيحي مبسط                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود Python
echo 🔍 التحقق من Python...

python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python متوفر
    goto :run_demo
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python متوفر (py)
    set PYTHON_CMD=py
    goto :run_demo
)

REM Python غير موجود
echo ❌ Python غير مثبت أو غير مضاف إلى PATH
echo.
echo 📥 يرجى تثبيت Python أولاً:
echo    1. اذهب إلى: https://www.python.org/downloads/
echo    2. حمل أحدث إصدار من Python
echo    3. أثناء التثبيت، تأكد من تحديد "Add Python to PATH"
echo    4. أعد تشغيل هذا الملف بعد التثبيت
echo.
echo 📖 للمزيد من التفاصيل، راجع ملف: INSTALL_PYTHON.md
echo.
pause
exit /b 1

:run_demo
if not defined PYTHON_CMD set PYTHON_CMD=python

echo.
echo 🚀 تشغيل العرض التوضيحي...
echo.

REM التحقق من وجود ملف العرض التوضيحي
if not exist "demo_app_simple.py" (
    echo ❌ ملف العرض التوضيحي غير موجود: demo_app_simple.py
    echo يرجى التأكد من وجود جميع ملفات المشروع
    pause
    exit /b 1
)

REM محاولة تثبيت Pillow إذا لم تكن مثبتة
echo 📦 التحقق من المكتبات المطلوبة...
%PYTHON_CMD% -c "import PIL" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  مكتبة Pillow مفقودة. جاري التثبيت...
    %PYTHON_CMD% -m pip install Pillow
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Pillow
        echo يرجى تثبيتها يدوياً: pip install Pillow
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت Pillow
)

REM إنشاء مجلد output إذا لم يكن موجوداً
if not exist "output" mkdir output

echo ✅ جميع المتطلبات جاهزة
echo.
echo 🎨 بدء تشغيل العرض التوضيحي...
echo.
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo   ستفتح نافذة التطبيق الآن
echo   يمكنك اختيار الصور وتحويلها إلى خريطة بكسلات
echo   أو اختيار خريطة وإعادة بناء الصورة منها
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.

REM تشغيل العرض التوضيحي
%PYTHON_CMD% demo_app_simple.py

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح
) else (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo.
    echo 🔍 أسباب محتملة:
    echo    - مكتبة tkinter غير مثبتة
    echo    - مكتبة Pillow غير مثبتة
    echo    - خطأ في ملف demo_app_simple.py
    echo.
    echo 💡 حلول مقترحة:
    echo    1. أعد تثبيت Python مع تحديد جميع المكونات
    echo    2. ثبت المكتبات: pip install Pillow
    echo    3. راجع ملف INSTALL_PYTHON.md للمساعدة
)

echo.
echo 📁 ملفات الإخراج محفوظة في مجلد: output\
if exist "output\*.json" (
    echo 📄 خرائط البكسلات:
    dir /b output\*.json 2>nul
)
if exist "output\*.png" (
    echo 🖼️  الصور المُعادة:
    dir /b output\*.png 2>nul
)

echo.
echo 📖 للمزيد من المعلومات:
echo    - README.md - الدليل الشامل
echo    - QUICK_START.md - البدء السريع
echo    - INSTALL_PYTHON.md - تثبيت Python
echo.
pause
