"""
وظائف مساعدة للتطبيق
"""

import os
import json
from typing import Dict, Any, <PERSON><PERSON>
from PIL import Image


def validate_image_file(file_path: str) -> bool:
    """
    التحقق من صحة ملف الصورة
    """
    if not os.path.exists(file_path):
        return False
    
    try:
        with Image.open(file_path) as img:
            img.verify()
        return True
    except Exception:
        return False


def get_file_size(file_path: str) -> int:
    """
    الحصول على حجم الملف بالبايت
    """
    if os.path.exists(file_path):
        return os.path.getsize(file_path)
    return 0


def format_file_size(size_bytes: int) -> str:
    """
    تنسيق حجم الملف بوحدة مناسبة
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.2f} {size_names[i]}"


def get_image_info(image_path: str) -> Dict[str, Any]:
    """
    الحصول على معلومات الصورة
    """
    try:
        with Image.open(image_path) as img:
            return {
                "width": img.width,
                "height": img.height,
                "mode": img.mode,
                "format": img.format,
                "size": img.width * img.height,
                "file_size": get_file_size(image_path)
            }
    except Exception as e:
        return {"error": str(e)}


def create_output_directory(path: str) -> None:
    """
    إنشاء مجلد الإخراج إذا لم يكن موجوداً
    """
    os.makedirs(path, exist_ok=True)


def rgb_to_hex(r: int, g: int, b: int) -> str:
    """
    تحويل RGB إلى HEX
    """
    return f"#{r:02x}{g:02x}{b:02x}"


def hex_to_rgb(hex_color: str) -> Tuple[int, int, int]:
    """
    تحويل HEX إلى RGB
    """
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))


def save_json_compressed(data: Any, file_path: str) -> None:
    """
    حفظ JSON مضغوط
    """
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, separators=(',', ':'), ensure_ascii=False)


def load_json(file_path: str) -> Any:
    """
    تحميل ملف JSON
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def print_comparison_stats(original_path: str, map_path: str) -> None:
    """
    طباعة إحصائيات المقارنة
    """
    original_size = get_file_size(original_path)
    map_size = get_file_size(map_path)
    
    compression_ratio = (1 - map_size / original_size) * 100 if original_size > 0 else 0
    
    print(f"\n📊 إحصائيات المقارنة:")
    print(f"   الصورة الأصلية: {format_file_size(original_size)}")
    print(f"   خريطة البكسلات: {format_file_size(map_size)}")
    print(f"   نسبة الضغط: {compression_ratio:.2f}%")
    
    if map_size > original_size:
        print(f"   ⚠️  تحذير: حجم الخريطة أكبر من الصورة الأصلية!")
    else:
        print(f"   ✅ تم توفير: {format_file_size(original_size - map_size)}")
