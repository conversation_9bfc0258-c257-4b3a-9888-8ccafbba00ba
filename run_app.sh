#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دالة طباعة الشعار
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🎨 Pixel Map Converter 🎨                 ║"
    echo "║                  تطبيق تحويل الصور إلى خريطة بكسلات          ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# دالة التحقق من Python
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ Python غير مثبت${NC}"
        echo "يرجى تثبيت Python 3.7 أو أحدث"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Python متوفر: $($PYTHON_CMD --version)${NC}"
}

# دالة التحقق من المتطلبات
check_requirements() {
    echo -e "${YELLOW}🔍 التحقق من المتطلبات...${NC}"
    
    if ! $PYTHON_CMD -c "import PIL, click, colorama, tqdm, numpy" &> /dev/null; then
        echo -e "${YELLOW}⚠️  بعض المتطلبات مفقودة. جاري التثبيت...${NC}"
        $PYTHON_CMD -m pip install -r requirements.txt
        
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ فشل في تثبيت المتطلبات${NC}"
            exit 1
        fi
    fi
    
    echo -e "${GREEN}✅ جميع المتطلبات متوفرة${NC}"
}

# دالة إنشاء صور تجريبية
create_test_images() {
    if [ ! -f "examples/simple_10x10.png" ]; then
        echo -e "${YELLOW}🎨 إنشاء صور تجريبية...${NC}"
        $PYTHON_CMD create_test_image.py
    fi
}

# دالة عرض القائمة
show_menu() {
    echo
    echo "═══════════════════════════════════════════════════════════════"
    echo "                           القائمة الرئيسية"
    echo "═══════════════════════════════════════════════════════════════"
    echo
    echo "1. تحويل صورة إلى خريطة بكسلات"
    echo "2. تحويل خريطة بكسلات إلى صورة"
    echo "3. عرض معلومات ملف"
    echo "4. اختبار شامل للتطبيق"
    echo "5. عرض المساعدة"
    echo "6. خروج"
    echo
}

# دالة تحويل صورة إلى خريطة
image_to_map() {
    echo
    echo -e "${YELLOW}🔄 تحويل صورة إلى خريطة بكسلات${NC}"
    echo "═══════════════════════════════════════"
    echo
    echo "الصور المتاحة في مجلد examples:"
    ls examples/*.png 2>/dev/null || echo "لا توجد صور"
    echo
    read -p "أدخل مسار ملف الصورة [examples/simple_10x10.png]: " image_file
    image_file=${image_file:-examples/simple_10x10.png}
    
    echo
    echo "جاري التحويل..."
    $PYTHON_CMD main.py image-to-map "$image_file"
    echo
    read -p "اضغط Enter للمتابعة..."
}

# دالة تحويل خريطة إلى صورة
map_to_image() {
    echo
    echo -e "${YELLOW}🔄 تحويل خريطة بكسلات إلى صورة${NC}"
    echo "═══════════════════════════════════════"
    echo
    echo "ملفات الخرائط المتاحة في مجلد output:"
    ls output/*_pixel_map.json 2>/dev/null || echo "لا توجد ملفات خرائط"
    echo
    read -p "أدخل مسار ملف الخريطة: " map_file
    
    if [ -z "$map_file" ]; then
        map_file=$(ls output/*_pixel_map.json 2>/dev/null | head -n1)
        if [ -z "$map_file" ]; then
            echo -e "${RED}❌ لا توجد ملفات خرائط. قم بتحويل صورة أولاً.${NC}"
            read -p "اضغط Enter للمتابعة..."
            return
        fi
    fi
    
    echo
    echo "جاري التحويل..."
    $PYTHON_CMD main.py map-to-image "$map_file"
    echo
    read -p "اضغط Enter للمتابعة..."
}

# دالة عرض معلومات
show_info() {
    echo
    echo -e "${YELLOW}ℹ️  عرض معلومات ملف${NC}"
    echo "═══════════════════════════════════════"
    echo
    read -p "أدخل مسار الملف [examples/simple_10x10.png]: " info_file
    info_file=${info_file:-examples/simple_10x10.png}
    
    echo
    $PYTHON_CMD main.py info "$info_file"
    echo
    read -p "اضغط Enter للمتابعة..."
}

# دالة تشغيل الاختبارات
run_tests() {
    echo
    echo -e "${YELLOW}🧪 تشغيل اختبار شامل${NC}"
    echo "═══════════════════════════════════════"
    echo
    $PYTHON_CMD test_app.py
    echo
    read -p "اضغط Enter للمتابعة..."
}

# دالة عرض المساعدة
show_help() {
    echo
    echo -e "${YELLOW}📖 المساعدة${NC}"
    echo "═══════════════════════════════════════"
    echo
    $PYTHON_CMD main.py --help
    echo
    echo "للمزيد من المعلومات، راجع ملف README.md"
    echo
    read -p "اضغط Enter للمتابعة..."
}

# الدالة الرئيسية
main() {
    print_banner
    check_python
    check_requirements
    create_test_images
    
    while true; do
        show_menu
        read -p "اختر رقماً (1-6): " choice
        
        case $choice in
            1) image_to_map ;;
            2) map_to_image ;;
            3) show_info ;;
            4) run_tests ;;
            5) show_help ;;
            6) 
                echo
                echo -e "${GREEN}👋 شكراً لاستخدام Pixel Map Converter!${NC}"
                echo
                exit 0
                ;;
            *) 
                echo -e "${RED}❌ اختيار غير صحيح${NC}"
                ;;
        esac
    done
}

# تشغيل التطبيق
main
