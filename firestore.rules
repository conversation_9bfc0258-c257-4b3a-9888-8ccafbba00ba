rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // قواعد الملفات المشفرة
    match /encrypted-files/{document} {
      // السماح بالقراءة والكتابة للملفات حسب بصمة المستخدم
      allow read, write: if resource == null || 
        resource.data.userFingerprint == request.query.userFingerprint ||
        (request.resource != null && 
         request.resource.data.userFingerprint is string &&
         request.resource.data.userFingerprint.size() > 0);
      
      // التحقق من صحة البيانات عند الكتابة
      allow create: if request.resource.data.keys().hasAll([
        'fileName', 'downloadURL', 'userFingerprint', 
        'uploadedAt', 'fileSize', 'fileType'
      ]) &&
      request.resource.data.fileName is string &&
      request.resource.data.fileName.size() > 0 &&
      request.resource.data.downloadURL is string &&
      request.resource.data.downloadURL.size() > 0 &&
      request.resource.data.userFingerprint is string &&
      request.resource.data.userFingerprint.size() >= 8 &&
      request.resource.data.fileSize is number &&
      request.resource.data.fileSize > 0 &&
      request.resource.data.fileType == 'encrypted-pixel-map';
      
      // السماح بالتحديث للمالك فقط
      allow update: if resource.data.userFingerprint == request.resource.data.userFingerprint;
      
      // السماح بالحذف للمالك فقط
      allow delete: if resource.data.userFingerprint == request.query.userFingerprint;
    }
    
    // قواعد إحصائيات الاستخدام
    match /usage-stats/{document} {
      // السماح بالكتابة فقط (لا قراءة للخصوصية)
      allow write: if request.resource.data.keys().hasAll([
        'action', 'timestamp'
      ]) &&
      request.resource.data.action is string &&
      request.resource.data.action.size() > 0 &&
      request.resource.data.timestamp is timestamp;
      
      // منع القراءة لحماية خصوصية المستخدمين
      allow read: if false;
    }
    
    // قواعد ملفات التعريف (اختيارية للمستقبل)
    match /user-profiles/{userId} {
      // السماح للمستخدم بقراءة وكتابة ملفه الشخصي فقط
      allow read, write: if request.auth != null && 
        request.auth.uid == userId;
    }
    
    // قواعد الإعدادات العامة (للقراءة فقط)
    match /app-settings/{document} {
      // السماح بالقراءة للجميع
      allow read: if true;
      
      // منع الكتابة (للمشرفين فقط)
      allow write: if false;
    }
    
    // قواعد التعليقات والتقييمات (للمستقبل)
    match /reviews/{document} {
      // السماح بالقراءة للجميع
      allow read: if true;
      
      // السماح بالكتابة مع التحقق من البيانات
      allow create: if request.resource.data.keys().hasAll([
        'rating', 'comment', 'timestamp', 'userFingerprint'
      ]) &&
      request.resource.data.rating is number &&
      request.resource.data.rating >= 1 &&
      request.resource.data.rating <= 5 &&
      request.resource.data.comment is string &&
      request.resource.data.comment.size() <= 500 &&
      request.resource.data.userFingerprint is string &&
      request.resource.data.userFingerprint.size() >= 8;
      
      // منع التحديث والحذف
      allow update, delete: if false;
    }
    
    // قواعد الأخطاء والتقارير
    match /error-reports/{document} {
      // السماح بالكتابة فقط لتسجيل الأخطاء
      allow write: if request.resource.data.keys().hasAll([
        'error', 'timestamp', 'userAgent'
      ]) &&
      request.resource.data.error is string &&
      request.resource.data.error.size() > 0 &&
      request.resource.data.timestamp is timestamp;
      
      // منع القراءة للخصوصية
      allow read: if false;
    }
    
    // منع الوصول لأي مجموعات أخرى
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
