# 📱 دليل بناء APK - Pixel Map Converter

## 🎯 نظرة عامة

هذا الدليل يوضح كيفية تحويل تطبيق **Pixel Map Converter** إلى ملف APK للأندرويد باستخدام **Buildozer** و **Kivy**.

## 📋 المتطلبات الأساسية

### 1. نظام التشغيل
- **Linux** (Ubuntu 18.04+ مُفضل)
- **macOS** (مع Xcode)
- **Windows** (مع WSL2 أو Linux VM)

### 2. البرمجيات المطلوبة
```bash
# Python 3.7+
python3 --version

# Git
git --version

# Java JDK 8
java -version

# Android SDK
echo $ANDROID_HOME
```

## 🔧 إعداد البيئة

### 1. تثبيت المتطلبات الأساسية

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install -y git zip unzip openjdk-8-jdk python3-pip autoconf libtool pkg-config zlib1g-dev libncurses5-dev libncursesw5-dev libtinfo5 cmake libffi-dev libssl-dev

# تثبيت Android SDK
sudo apt install android-sdk
```

#### macOS:
```bash
# تثبيت Homebrew إذا لم يكن مثبت
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# تثبيت المتطلبات
brew install python3 git openjdk@8 autoconf automake libtool pkg-config
brew install --cask android-sdk
```

### 2. تثبيت Python Dependencies
```bash
# تثبيت buildozer
pip3 install buildozer

# تثبيت Cython (مطلوب لـ Kivy)
pip3 install cython

# تثبيت متطلبات التطبيق
pip3 install -r requirements.txt
```

### 3. إعداد Android SDK

#### تحميل Android SDK:
```bash
# إنشاء مجلد للـ SDK
mkdir -p ~/android-sdk
cd ~/android-sdk

# تحميل command line tools
wget https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip
unzip commandlinetools-linux-8512546_latest.zip

# إعداد متغيرات البيئة
export ANDROID_HOME=~/android-sdk
export ANDROID_SDK_ROOT=$ANDROID_HOME
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools
```

#### تثبيت SDK Components:
```bash
# قبول التراخيص
yes | sdkmanager --licenses

# تثبيت المكونات المطلوبة
sdkmanager "platform-tools" "platforms;android-30" "build-tools;30.0.3"
```

### 4. إعداد متغيرات البيئة
```bash
# إضافة إلى ~/.bashrc أو ~/.zshrc
echo 'export ANDROID_HOME=~/android-sdk' >> ~/.bashrc
echo 'export ANDROID_SDK_ROOT=$ANDROID_HOME' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools' >> ~/.bashrc

# إعادة تحميل البيئة
source ~/.bashrc
```

## 🚀 بناء APK

### الطريقة 1: استخدام السكريبت التلقائي
```bash
# تشغيل سكريبت البناء
python3 build_apk.py
```

### الطريقة 2: البناء اليدوي

#### 1. تحضير الملفات:
```bash
# نسخ الملف الرئيسي
cp android_app.py main.py
```

#### 2. تهيئة buildozer:
```bash
# إنشاء ملف buildozer.spec (إذا لم يكن موجود)
buildozer init

# أو استخدام الملف الموجود
# buildozer.spec موجود بالفعل
```

#### 3. بناء APK للاختبار:
```bash
# بناء debug APK
buildozer android debug

# الملف سيكون في مجلد bin/
ls -la bin/*.apk
```

#### 4. بناء APK للإصدار النهائي:
```bash
# بناء release APK
buildozer android release

# توقيع APK (اختياري)
# jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore bin/pixelmapconverter-1.0-release-unsigned.apk alias_name
```

## 📁 هيكل الملفات للبناء

```
pixel_map_converter/
├── android_app.py          # التطبيق الرئيسي للأندرويد
├── main.py                 # نقطة الدخول (ينشأ تلقائياً)
├── buildozer.spec          # إعدادات البناء
├── build_apk.py           # سكريبت البناء
├── src/                   # الكود المصدري
│   ├── __init__.py
│   ├── image_to_map.py
│   ├── map_to_image.py
│   ├── compression.py
│   └── utils.py
├── requirements.txt       # المتطلبات (محدثة لـ Kivy)
├── .buildozer/           # ملفات البناء (ينشأ تلقائياً)
└── bin/                  # ملفات APK (ينشأ تلقائياً)
    └── pixelmapconverter-1.0-debug.apk
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ "buildozer command not found"
```bash
# تأكد من تثبيت buildozer
pip3 install --user buildozer
export PATH=$PATH:~/.local/bin
```

#### 2. خطأ "Android SDK not found"
```bash
# تأكد من تعيين متغيرات البيئة
echo $ANDROID_HOME
echo $ANDROID_SDK_ROOT

# إعادة تعيين المتغيرات
export ANDROID_HOME=~/android-sdk
export ANDROID_SDK_ROOT=$ANDROID_HOME
```

#### 3. خطأ "Java not found"
```bash
# تثبيت Java JDK 8
sudo apt install openjdk-8-jdk

# تعيين JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
```

#### 4. خطأ في بناء Kivy
```bash
# تثبيت المتطلبات الإضافية
sudo apt install build-essential libffi-dev python3-dev

# تحديث buildozer
pip3 install --upgrade buildozer
```

#### 5. خطأ "Permission denied"
```bash
# إعطاء صلاحيات للملفات
chmod +x build_apk.py
chmod -R 755 .buildozer/
```

## 📱 اختبار APK

### 1. تثبيت على الجهاز:
```bash
# تفعيل USB Debugging على الجهاز
# ثم تثبيت APK
adb install bin/pixelmapconverter-1.0-debug.apk
```

### 2. اختبار على المحاكي:
```bash
# تشغيل المحاكي
emulator -avd Pixel_4_API_30

# تثبيت APK
adb install bin/pixelmapconverter-1.0-debug.apk
```

## 📊 معلومات APK

### حجم APK المتوقع:
- **Debug APK**: ~50-80 MB
- **Release APK**: ~30-50 MB (بعد التحسين)

### الصلاحيات المطلوبة:
- `READ_EXTERNAL_STORAGE` - قراءة الصور
- `WRITE_EXTERNAL_STORAGE` - حفظ الملفات
- `INTERNET` - (للتحديثات المستقبلية)

## 🎯 تحسين APK

### تقليل حجم APK:
```bash
# استخدام ProGuard (في buildozer.spec)
android.add_compile_options = "minifyEnabled true"

# إزالة المكتبات غير المستخدمة
# تحديد requirements بدقة في buildozer.spec
```

### تحسين الأداء:
```bash
# استخدام arm64-v8a للأجهزة الحديثة
android.archs = arm64-v8a

# تفعيل التحسينات
android.release_artifact = aab
```

## 📞 الحصول على المساعدة

### مصادر مفيدة:
- [Buildozer Documentation](https://buildozer.readthedocs.io/)
- [Kivy Android Documentation](https://kivy.org/doc/stable/guide/packaging-android.html)
- [Python-for-Android](https://python-for-android.readthedocs.io/)

### أوامر مفيدة للتشخيص:
```bash
# عرض معلومات buildozer
buildozer --version

# تنظيف البناء
buildozer android clean

# عرض سجل الأخطاء
buildozer android debug -v
```

---

**استمتع بتطبيق Pixel Map Converter على الأندرويد! 📱🎨**
