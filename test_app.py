#!/usr/bin/env python3
"""
اختبار شامل لتطبيق Pixel Map Converter
"""

import os
import sys
import subprocess
from colorama import init, Fore, Style

# تهيئة colorama
init()

def run_command(command):
    """تشغيل أمر وإرجاع النتيجة"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def test_dependencies():
    """اختبار المتطلبات"""
    print(f"{Fore.YELLOW}🔍 اختبار المتطلبات...{Style.RESET_ALL}")
    
    required_packages = ['PIL', 'click', 'colorama', 'tqdm', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - غير مثبت")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n{Fore.RED}❌ مكتبات مفقودة: {', '.join(missing_packages)}{Style.RESET_ALL}")
        print("قم بتثبيتها باستخدام: pip install -r requirements.txt")
        return False
    
    print(f"{Fore.GREEN}✅ جميع المتطلبات متوفرة{Style.RESET_ALL}")
    return True

def test_create_images():
    """اختبار إنشاء الصور التجريبية"""
    print(f"\n{Fore.YELLOW}🎨 إنشاء صور تجريبية...{Style.RESET_ALL}")
    
    success, stdout, stderr = run_command("python create_test_image.py")
    
    if success:
        print(f"{Fore.GREEN}✅ تم إنشاء الصور التجريبية{Style.RESET_ALL}")
        return True
    else:
        print(f"{Fore.RED}❌ فشل في إنشاء الصور التجريبية{Style.RESET_ALL}")
        print(f"خطأ: {stderr}")
        return False

def test_image_to_map():
    """اختبار تحويل صورة إلى خريطة"""
    print(f"\n{Fore.YELLOW}🔄 اختبار تحويل صورة إلى خريطة...{Style.RESET_ALL}")
    
    test_cases = [
        ("examples/simple_10x10.png", "صورة بسيطة 10x10"),
        ("examples/medium_50x50.png", "صورة متوسطة 50x50"),
        ("examples/transparent_30x30.png", "صورة مع شفافية"),
    ]
    
    success_count = 0
    
    for image_path, description in test_cases:
        if not os.path.exists(image_path):
            print(f"   ⚠️  {description}: الملف غير موجود")
            continue
        
        print(f"   🔄 اختبار {description}...")
        
        command = f"python main.py image-to-map {image_path}"
        success, stdout, stderr = run_command(command)
        
        if success:
            print(f"   ✅ {description}: نجح التحويل")
            success_count += 1
        else:
            print(f"   ❌ {description}: فشل التحويل")
            print(f"      خطأ: {stderr}")
    
    print(f"\n📊 نتائج اختبار التحويل: {success_count}/{len(test_cases)}")
    return success_count == len(test_cases)

def test_map_to_image():
    """اختبار تحويل خريطة إلى صورة"""
    print(f"\n{Fore.YELLOW}🔄 اختبار تحويل خريطة إلى صورة...{Style.RESET_ALL}")
    
    # البحث عن ملفات الخرائط المُنشأة
    map_files = []
    if os.path.exists("output"):
        for file in os.listdir("output"):
            if file.endswith("_pixel_map.json"):
                map_files.append(os.path.join("output", file))
    
    if not map_files:
        print(f"   ⚠️  لا توجد ملفات خرائط للاختبار")
        return False
    
    success_count = 0
    
    for map_file in map_files:
        print(f"   🔄 اختبار {os.path.basename(map_file)}...")
        
        command = f"python main.py map-to-image {map_file}"
        success, stdout, stderr = run_command(command)
        
        if success:
            print(f"   ✅ {os.path.basename(map_file)}: نجح التحويل")
            success_count += 1
        else:
            print(f"   ❌ {os.path.basename(map_file)}: فشل التحويل")
            print(f"      خطأ: {stderr}")
    
    print(f"\n📊 نتائج اختبار إعادة البناء: {success_count}/{len(map_files)}")
    return success_count == len(map_files)

def test_info_command():
    """اختبار أمر عرض المعلومات"""
    print(f"\n{Fore.YELLOW}ℹ️  اختبار أمر المعلومات...{Style.RESET_ALL}")
    
    # اختبار معلومات صورة
    if os.path.exists("examples/simple_10x10.png"):
        command = "python main.py info examples/simple_10x10.png"
        success, stdout, stderr = run_command(command)
        
        if success:
            print(f"   ✅ معلومات الصورة: نجح")
        else:
            print(f"   ❌ معلومات الصورة: فشل")
            return False
    
    # اختبار معلومات خريطة
    map_files = []
    if os.path.exists("output"):
        for file in os.listdir("output"):
            if file.endswith("_pixel_map.json"):
                map_files.append(os.path.join("output", file))
                break
    
    if map_files:
        command = f"python main.py info {map_files[0]}"
        success, stdout, stderr = run_command(command)
        
        if success:
            print(f"   ✅ معلومات الخريطة: نجح")
        else:
            print(f"   ❌ معلومات الخريطة: فشل")
            return False
    
    return True

def test_file_sizes():
    """مقارنة أحجام الملفات"""
    print(f"\n{Fore.YELLOW}📊 مقارنة أحجام الملفات...{Style.RESET_ALL}")
    
    comparisons = []
    
    if os.path.exists("examples") and os.path.exists("output"):
        for example_file in os.listdir("examples"):
            if example_file.endswith(".png"):
                original_path = os.path.join("examples", example_file)
                base_name = os.path.splitext(example_file)[0]
                map_path = os.path.join("output", f"{base_name}_pixel_map.json")
                
                if os.path.exists(map_path):
                    original_size = os.path.getsize(original_path)
                    map_size = os.path.getsize(map_path)
                    
                    ratio = (map_size / original_size) * 100 if original_size > 0 else 0
                    
                    print(f"   📁 {example_file}:")
                    print(f"      الأصلي: {original_size:,} بايت")
                    print(f"      الخريطة: {map_size:,} بايت")
                    print(f"      النسبة: {ratio:.1f}%")
                    
                    comparisons.append((example_file, ratio))
    
    if comparisons:
        avg_ratio = sum(ratio for _, ratio in comparisons) / len(comparisons)
        print(f"\n   📈 متوسط نسبة الحجم: {avg_ratio:.1f}%")
    
    return True

def main():
    """الاختبار الرئيسي"""
    print(f"{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗")
    print(f"║                    🧪 اختبار شامل للتطبيق 🧪                ║")
    print(f"║                  Pixel Map Converter Test                    ║")
    print(f"╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}")
    
    tests = [
        ("اختبار المتطلبات", test_dependencies),
        ("إنشاء صور تجريبية", test_create_images),
        ("تحويل صورة إلى خريطة", test_image_to_map),
        ("تحويل خريطة إلى صورة", test_map_to_image),
        ("اختبار أمر المعلومات", test_info_command),
        ("مقارنة أحجام الملفات", test_file_sizes),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                print(f"{Fore.RED}❌ فشل في: {test_name}{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}❌ خطأ في {test_name}: {str(e)}{Style.RESET_ALL}")
    
    # النتائج النهائية
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📊 نتائج الاختبار النهائية:{Style.RESET_ALL}")
    print(f"   الاختبارات الناجحة: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print(f"{Fore.GREEN}🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام.{Style.RESET_ALL}")
    else:
        print(f"{Fore.YELLOW}⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.{Style.RESET_ALL}")
    
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
