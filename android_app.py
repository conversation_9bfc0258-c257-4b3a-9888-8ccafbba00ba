#!/usr/bin/env python3
"""
تطبيق Pixel Map Converter للأندرويد
باستخدام Kivy و KivyMD
"""

import os
import sys
import json
import threading
from io import BytesIO

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.image import Image
from kivy.uix.popup import Popup
from kivy.uix.filechooser import FileChooserIconView
from kivy.uix.progressbar import ProgressBar
from kivy.uix.textinput import TextInput
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
from kivy.core.window import Window
from kivy.utils import platform

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.navigationdrawer import MDNavigationDrawer
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar import Snackbar

try:
    from plyer import filechooser
    PLYER_AVAILABLE = True
except ImportError:
    PLYER_AVAILABLE = False

# استيراد وحدات التطبيق
try:
    from src.image_to_map import ImageToMapConverter
    from src.map_to_image import MapToImageConverter
    from src.utils import validate_image_file, get_image_info, format_file_size
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")


class PixelMapConverterApp(MDApp):
    """التطبيق الرئيسي"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Pixel Map Converter"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"

        # متغيرات التطبيق
        self.current_image_path = None
        self.current_map_path = None
        self.converter_image_to_map = ImageToMapConverter()
        self.converter_map_to_image = MapToImageConverter()

        # إنشاء مجلدات الإخراج
        self.setup_directories()

    def setup_directories(self):
        """إنشاء المجلدات المطلوبة"""
        try:
            if platform == 'android':
                from android.storage import primary_external_storage_path
                self.app_dir = os.path.join(primary_external_storage_path(), 'PixelMapConverter')
            else:
                self.app_dir = os.path.join(os.path.expanduser('~'), 'PixelMapConverter')

            self.output_dir = os.path.join(self.app_dir, 'output')
            self.examples_dir = os.path.join(self.app_dir, 'examples')

            os.makedirs(self.output_dir, exist_ok=True)
            os.makedirs(self.examples_dir, exist_ok=True)

        except Exception as e:
            print(f"خطأ في إنشاء المجلدات: {e}")
            self.app_dir = os.getcwd()
            self.output_dir = os.path.join(self.app_dir, 'output')
            self.examples_dir = os.path.join(self.app_dir, 'examples')

    def build(self):
        """بناء واجهة التطبيق"""
        self.screen = MDScreen()

        # الشريط العلوي
        toolbar = MDTopAppBar(
            title="Pixel Map Converter",
            elevation=2,
            md_bg_color=self.theme_cls.primary_color
        )

        # المحتوى الرئيسي
        main_layout = MDBoxLayout(
            orientation='vertical',
            spacing="10dp",
            adaptive_height=True,
            padding="20dp"
        )

        # بطاقة الترحيب
        welcome_card = self.create_welcome_card()
        main_layout.add_widget(welcome_card)

        # بطاقة تحويل الصور
        image_to_map_card = self.create_image_to_map_card()
        main_layout.add_widget(image_to_map_card)

        # بطاقة إعادة بناء الصور
        map_to_image_card = self.create_map_to_image_card()
        main_layout.add_widget(map_to_image_card)

        # بطاقة المعلومات
        info_card = self.create_info_card()
        main_layout.add_widget(info_card)

        # إضافة المكونات للشاشة
        self.screen.add_widget(toolbar)

        # ScrollView للمحتوى
        scroll = ScrollView()
        scroll.add_widget(main_layout)
        self.screen.add_widget(scroll)

        return self.screen

    def create_welcome_card(self):
        """إنشاء بطاقة الترحيب"""
        card = MDCard(
            orientation='vertical',
            padding="15dp",
            size_hint_y=None,
            height="120dp",
            elevation=2,
            md_bg_color=self.theme_cls.bg_light
        )

        title = MDLabel(
            text="🎨 مرحباً بك في محول خريطة البكسلات",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            font_style="H6"
        )

        subtitle = MDLabel(
            text="حول صورك إلى خريطة بكسلات وأعد بناءها مرة أخرى",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="30dp"
        )

        card.add_widget(title)
        card.add_widget(subtitle)

        return card

    def create_image_to_map_card(self):
        """إنشاء بطاقة تحويل الصور"""
        card = MDCard(
            orientation='vertical',
            padding="15dp",
            size_hint_y=None,
            height="200dp",
            elevation=2
        )

        title = MDLabel(
            text="📷 تحويل صورة إلى خريطة بكسلات",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            font_style="Subtitle1"
        )

        select_btn = MDRaisedButton(
            text="اختيار صورة",
            size_hint_y=None,
            height="40dp",
            on_release=self.select_image_file
        )

        convert_btn = MDRaisedButton(
            text="تحويل إلى خريطة",
            size_hint_y=None,
            height="40dp",
            disabled=True,
            on_release=self.convert_image_to_map
        )

        self.image_status_label = MDLabel(
            text="لم يتم اختيار صورة",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="30dp"
        )

        card.add_widget(title)
        card.add_widget(select_btn)
        card.add_widget(self.image_status_label)
        card.add_widget(convert_btn)

        # حفظ مرجع للزر
        self.convert_image_btn = convert_btn

        return card

    def create_map_to_image_card(self):
        """إنشاء بطاقة إعادة بناء الصور"""
        card = MDCard(
            orientation='vertical',
            padding="15dp",
            size_hint_y=None,
            height="200dp",
            elevation=2
        )

        title = MDLabel(
            text="🗺️ تحويل خريطة إلى صورة",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            font_style="Subtitle1"
        )

        select_btn = MDRaisedButton(
            text="اختيار خريطة",
            size_hint_y=None,
            height="40dp",
            on_release=self.select_map_file
        )

        rebuild_btn = MDRaisedButton(
            text="إعادة بناء الصورة",
            size_hint_y=None,
            height="40dp",
            disabled=True,
            on_release=self.convert_map_to_image
        )

        self.map_status_label = MDLabel(
            text="لم يتم اختيار خريطة",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="30dp"
        )

        card.add_widget(title)
        card.add_widget(select_btn)
        card.add_widget(self.map_status_label)
        card.add_widget(rebuild_btn)

        # حفظ مرجع للزر
        self.rebuild_image_btn = rebuild_btn

        return card

    def create_info_card(self):
        """إنشاء بطاقة المعلومات"""
        card = MDCard(
            orientation='vertical',
            padding="15dp",
            size_hint_y=None,
            height="150dp",
            elevation=2
        )

        title = MDLabel(
            text="ℹ️ معلومات التطبيق",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            font_style="Subtitle1"
        )

        info_text = MDLabel(
            text=f"مجلد الإخراج: {self.output_dir}\nالإصدار: 1.0.0\nالمطور: Augment Agent",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="80dp"
        )

        card.add_widget(title)
        card.add_widget(info_text)

        return card

    def select_image_file(self, instance):
        """اختيار ملف صورة"""
        if PLYER_AVAILABLE and platform == 'android':
            try:
                filechooser.open_file(
                    on_selection=self.handle_image_selection,
                    filters=['*.png', '*.jpg', '*.jpeg', '*.bmp']
                )
            except Exception as e:
                self.show_error(f"خطأ في اختيار الملف: {str(e)}")
        else:
            # للاختبار على سطح المكتب
            self.show_file_chooser_popup("image")

    def select_map_file(self, instance):
        """اختيار ملف خريطة"""
        if PLYER_AVAILABLE and platform == 'android':
            try:
                filechooser.open_file(
                    on_selection=self.handle_map_selection,
                    filters=['*.json']
                )
            except Exception as e:
                self.show_error(f"خطأ في اختيار الملف: {str(e)}")
        else:
            # للاختبار على سطح المكتب
            self.show_file_chooser_popup("map")

    def show_file_chooser_popup(self, file_type):
        """عرض نافذة اختيار الملفات (للاختبار)"""
        content = BoxLayout(orientation='vertical', spacing=10)

        filechooser = FileChooserIconView(
            size_hint=(1, 0.9),
            filters=['*.png', '*.jpg', '*.jpeg', '*.bmp'] if file_type == 'image' else ['*.json']
        )

        buttons_layout = BoxLayout(size_hint_y=None, height=50, spacing=10)

        select_btn = Button(text="اختيار", size_hint_x=0.5)
        cancel_btn = Button(text="إلغاء", size_hint_x=0.5)

        def on_select(instance):
            if filechooser.selection:
                if file_type == 'image':
                    self.handle_image_selection(filechooser.selection)
                else:
                    self.handle_map_selection(filechooser.selection)
            popup.dismiss()

        def on_cancel(instance):
            popup.dismiss()

        select_btn.bind(on_release=on_select)
        cancel_btn.bind(on_release=on_cancel)

        buttons_layout.add_widget(select_btn)
        buttons_layout.add_widget(cancel_btn)

        content.add_widget(filechooser)
        content.add_widget(buttons_layout)

        popup = Popup(
            title=f"اختيار {'صورة' if file_type == 'image' else 'خريطة'}",
            content=content,
            size_hint=(0.9, 0.9)
        )
        popup.open()

    def handle_image_selection(self, selection):
        """معالجة اختيار الصورة"""
        if selection:
            self.current_image_path = selection[0]
            filename = os.path.basename(self.current_image_path)

            if validate_image_file(self.current_image_path):
                self.image_status_label.text = f"تم اختيار: {filename}"
                self.convert_image_btn.disabled = False

                # عرض معلومات الصورة
                info = get_image_info(self.current_image_path)
                details = f"الأبعاد: {info.get('width')}x{info.get('height')}\nالحجم: {format_file_size(info.get('file_size', 0))}"
                self.show_info(f"معلومات الصورة:\n{details}")
            else:
                self.show_error("ملف الصورة غير صحيح")
                self.current_image_path = None

    def handle_map_selection(self, selection):
        """معالجة اختيار الخريطة"""
        if selection:
            self.current_map_path = selection[0]
            filename = os.path.basename(self.current_map_path)

            if self.converter_map_to_image.validate_map_file(self.current_map_path):
                self.map_status_label.text = f"تم اختيار: {filename}"
                self.rebuild_image_btn.disabled = False

                # عرض معلومات الخريطة
                info = self.converter_map_to_image.get_map_info(self.current_map_path)
                details = f"الأبعاد: {info.get('dimensions')}\nالبكسلات: {info.get('pixel_count', 0):,}"
                self.show_info(f"معلومات الخريطة:\n{details}")
            else:
                self.show_error("ملف الخريطة غير صحيح")
                self.current_map_path = None

    def convert_image_to_map(self, instance):
        """تحويل صورة إلى خريطة"""
        if not self.current_image_path:
            self.show_error("يرجى اختيار صورة أولاً")
            return

        # تشغيل التحويل في خيط منفصل
        threading.Thread(target=self._convert_image_to_map_thread).start()
        self.show_progress("جاري تحويل الصورة...")

    def _convert_image_to_map_thread(self):
        """تحويل الصورة في خيط منفصل"""
        try:
            filename = os.path.splitext(os.path.basename(self.current_image_path))[0]
            output_path = os.path.join(self.output_dir, f"{filename}_pixel_map.json")

            success = self.converter_image_to_map.convert_image_to_map(
                image_path=self.current_image_path,
                output_path=output_path,
                compress_colors=True,
                skip_transparent=True
            )

            Clock.schedule_once(lambda dt: self._on_conversion_complete(success, output_path))

        except Exception as e:
            Clock.schedule_once(lambda dt: self.show_error(f"خطأ في التحويل: {str(e)}"))

    def convert_map_to_image(self, instance):
        """تحويل خريطة إلى صورة"""
        if not self.current_map_path:
            self.show_error("يرجى اختيار خريطة أولاً")
            return

        # تشغيل التحويل في خيط منفصل
        threading.Thread(target=self._convert_map_to_image_thread).start()
        self.show_progress("جاري إعادة بناء الصورة...")

    def _convert_map_to_image_thread(self):
        """تحويل الخريطة في خيط منفصل"""
        try:
            filename = os.path.splitext(os.path.basename(self.current_map_path))[0]
            output_path = os.path.join(self.output_dir, f"{filename}_restored.png")

            success = self.converter_map_to_image.convert_map_to_image(
                map_path=self.current_map_path,
                output_path=output_path,
                background_color=(255, 255, 255, 255)
            )

            Clock.schedule_once(lambda dt: self._on_rebuild_complete(success, output_path))

        except Exception as e:
            Clock.schedule_once(lambda dt: self.show_error(f"خطأ في إعادة البناء: {str(e)}"))

    def _on_conversion_complete(self, success, output_path):
        """عند اكتمال التحويل"""
        if success:
            self.show_success(f"تم التحويل بنجاح!\nالملف محفوظ في:\n{output_path}")
        else:
            self.show_error("فشل في تحويل الصورة")

    def _on_rebuild_complete(self, success, output_path):
        """عند اكتمال إعادة البناء"""
        if success:
            self.show_success(f"تم إعادة البناء بنجاح!\nالصورة محفوظة في:\n{output_path}")
        else:
            self.show_error("فشل في إعادة بناء الصورة")

    def show_progress(self, message):
        """عرض رسالة التقدم"""
        Snackbar(text=message).open()

    def show_success(self, message):
        """عرض رسالة نجاح"""
        dialog = MDDialog(
            title="نجح العملية",
            text=message,
            buttons=[
                MDRaisedButton(
                    text="موافق",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def show_error(self, message):
        """عرض رسالة خطأ"""
        dialog = MDDialog(
            title="خطأ",
            text=message,
            buttons=[
                MDRaisedButton(
                    text="موافق",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def show_info(self, message):
        """عرض رسالة معلومات"""
        dialog = MDDialog(
            title="معلومات",
            text=message,
            buttons=[
                MDRaisedButton(
                    text="موافق",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()


# تشغيل التطبيق
if __name__ == '__main__':
    PixelMapConverterApp().run()
