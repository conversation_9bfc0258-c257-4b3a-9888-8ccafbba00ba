# 🚀 البدء السريع - Pixel Map Converter

## 📥 التثبيت السريع

### 1. تأكد من تثبيت Python
```bash
# تحقق من وجود Python
python --version
# أو
python3 --version
```

إذا لم يكن Python مثبتاً، قم بتحميله من: https://www.python.org/downloads/

### 2. تثبيت المتطلبات
```bash
# Windows
python -m pip install -r requirements.txt

# macOS/Linux  
python3 -m pip install -r requirements.txt
```

## 🎯 الاستخدام السريع

### الطريقة 1: الواجهة التفاعلية (الأسهل)

#### Windows:
```cmd
run_app.bat
```

#### macOS/Linux:
```bash
chmod +x run_app.sh
./run_app.sh
```

### الطريقة 2: سطر الأوامر المباشر

#### إنشاء صور تجريبية:
```bash
python create_test_image.py
```

#### تحويل صورة إلى خريطة:
```bash
python main.py image-to-map examples/simple_10x10.png
```

#### إعادة بناء صورة من خريطة:
```bash
python main.py map-to-image output/simple_10x10_pixel_map.json
```

#### عرض معلومات ملف:
```bash
python main.py info examples/simple_10x10.png
```

### الطريقة 3: العرض التوضيحي البسيط
```bash
python simple_demo.py
```

## 🧪 اختبار التطبيق

```bash
python test_app.py
```

## 📖 أمثلة متقدمة

### تحويل مع خيارات متقدمة:
```bash
# تحويل مع ضغط وتجاهل الشفافية
python main.py image-to-map image.png --compress --skip-transparent

# إعادة بناء بتنسيق JPEG وخلفية سوداء
python main.py map-to-image pixel_map.json --format jpg --background black
```

### معالجة دفعية:
```bash
# Windows
for %f in (*.png) do python main.py image-to-map "%f"

# Linux/macOS
for img in *.png; do python main.py image-to-map "$img"; done
```

## 🔍 استكشاف الأخطاء السريع

### مشكلة: "python is not recognized"
**الحل:**
1. تأكد من تثبيت Python
2. أضف Python إلى PATH
3. جرب `py` بدلاً من `python` في Windows

### مشكلة: "No module named 'PIL'"
**الحل:**
```bash
python -m pip install Pillow
```

### مشكلة: "Permission denied"
**الحل:**
```bash
python -m pip install --user -r requirements.txt
```

## 📁 هيكل الملفات المتوقع بعد التشغيل

```
pixel_map_converter/
├── examples/               # صور تجريبية
│   ├── simple_10x10.png
│   ├── medium_50x50.png
│   ├── transparent_30x30.png
│   ├── chess_40x40.png
│   └── gradient_60x20.png
├── output/                # ملفات الإخراج
│   ├── simple_10x10_pixel_map.json
│   ├── simple_10x10_restored.png
│   └── ...
└── ...
```

## 🎉 تأكيد نجاح التثبيت

إذا تمكنت من تشغيل هذه الأوامر بنجاح، فالتطبيق يعمل بشكل صحيح:

```bash
# 1. إنشاء صور تجريبية
python create_test_image.py

# 2. تحويل صورة
python main.py image-to-map examples/simple_10x10.png

# 3. إعادة بناء صورة  
python main.py map-to-image output/simple_10x10_pixel_map.json

# 4. عرض المساعدة
python main.py --help
```

## 📞 الحصول على المساعدة

- راجع `README.md` للدليل الشامل
- راجع `INSTALL.md` لتعليمات التثبيت المفصلة
- راجع `PROJECT_SUMMARY.md` لنظرة عامة على المشروع

---

**استمتع بتحويل صورك إلى خرائط بكسلات! 🎨✨**
