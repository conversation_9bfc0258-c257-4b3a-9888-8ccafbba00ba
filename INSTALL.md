# 🚀 دليل التثبيت والإعداد - Pixel Map Converter

## 📋 المتطلبات الأساسية

### 1. تثبيت Python
يجب تثبيت Python 3.7 أو أحدث على نظامك.

#### Windows:
1. قم بتحميل Python من الموقع الرسمي: https://www.python.org/downloads/
2. أثناء التثبيت، تأكد من تحديد "Add Python to PATH"
3. تحقق من التثبيت بفتح Command Prompt وكتابة:
   ```cmd
   python --version
   ```

#### macOS:
```bash
# باستخدام Homebrew
brew install python

# أو تحميل من الموقع الرسمي
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install python3 python3-pip
```

### 2. التحقق من تثبيت Python
```bash
# Windows
python --version
python -m pip --version

# macOS/Linux
python3 --version
python3 -m pip --version
```

## 🔧 تثبيت التطبيق

### الطريقة 1: التثبيت التلقائي
```bash
# Windows
python -m pip install -r requirements.txt

# macOS/Linux
python3 -m pip install -r requirements.txt
```

### الطريقة 2: التثبيت اليدوي
إذا فشلت الطريقة الأولى، قم بتثبيت كل مكتبة منفصلة:

```bash
# Windows
python -m pip install Pillow>=10.0.0
python -m pip install numpy>=1.24.0
python -m pip install click>=8.1.0
python -m pip install colorama>=0.4.6
python -m pip install tqdm>=4.65.0

# macOS/Linux
python3 -m pip install Pillow>=10.0.0
python3 -m pip install numpy>=1.24.0
python3 -m pip install click>=8.1.0
python3 -m pip install colorama>=0.4.6
python3 -m pip install tqdm>=4.65.0
```

## 🧪 اختبار التثبيت

### 1. إنشاء صور تجريبية
```bash
# Windows
python create_test_image.py

# macOS/Linux
python3 create_test_image.py
```

### 2. اختبار التطبيق الأساسي
```bash
# Windows
python main.py --help

# macOS/Linux
python3 main.py --help
```

### 3. اختبار شامل
```bash
# Windows
python test_app.py

# macOS/Linux
python3 test_app.py
```

## 🎯 أمثلة سريعة للاستخدام

### تحويل صورة إلى خريطة بكسلات
```bash
# Windows
python main.py image-to-map examples/simple_10x10.png

# macOS/Linux
python3 main.py image-to-map examples/simple_10x10.png
```

### إعادة بناء صورة من خريطة
```bash
# Windows
python main.py map-to-image output/simple_10x10_pixel_map.json

# macOS/Linux
python3 main.py map-to-image output/simple_10x10_pixel_map.json
```

### عرض معلومات ملف
```bash
# Windows
python main.py info examples/simple_10x10.png

# macOS/Linux
python3 main.py info examples/simple_10x10.png
```

## 🔍 استكشاف الأخطاء

### مشكلة: "python is not recognized"
**الحل:**
1. تأكد من تثبيت Python بشكل صحيح
2. أضف Python إلى متغير البيئة PATH
3. أعد تشغيل Command Prompt/Terminal
4. جرب استخدام `py` بدلاً من `python` في Windows

### مشكلة: "No module named 'PIL'"
**الحل:**
```bash
python -m pip install --upgrade Pillow
```

### مشكلة: "Permission denied"
**الحل:**
```bash
# Windows (تشغيل كمدير)
python -m pip install --user -r requirements.txt

# macOS/Linux
python3 -m pip install --user -r requirements.txt
```

### مشكلة: "SSL Certificate error"
**الحل:**
```bash
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
```

## 📁 هيكل المشروع بعد التثبيت

```
pixel_map_converter/
├── src/                    # الكود المصدري
│   ├── __init__.py
│   ├── image_to_map.py
│   ├── map_to_image.py
│   ├── compression.py
│   └── utils.py
├── examples/               # صور تجريبية
│   ├── simple_10x10.png
│   ├── medium_50x50.png
│   ├── transparent_30x30.png
│   ├── chess_40x40.png
│   └── gradient_60x20.png
├── output/                # ملفات الإخراج
├── main.py               # الملف الرئيسي
├── create_test_image.py  # إنشاء صور تجريبية
├── test_app.py          # اختبار شامل
├── requirements.txt     # المتطلبات
├── README.md           # دليل الاستخدام
└── INSTALL.md         # هذا الملف
```

## 🎉 التحقق من نجاح التثبيت

إذا تمكنت من تشغيل الأوامر التالية بنجاح، فإن التطبيق جاهز للاستخدام:

```bash
# 1. إنشاء صور تجريبية
python create_test_image.py

# 2. تحويل صورة إلى خريطة
python main.py image-to-map examples/simple_10x10.png

# 3. إعادة بناء الصورة
python main.py map-to-image output/simple_10x10_pixel_map.json

# 4. عرض المعلومات
python main.py info examples/simple_10x10.png
```

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:
1. تأكد من تثبيت Python بشكل صحيح
2. تحقق من تثبيت جميع المتطلبات
3. جرب الأمثلة البسيطة أولاً
4. استخدم `python main.py --help` لعرض المساعدة

---

**استمتع باستخدام Pixel Map Converter! 🎨✨**
