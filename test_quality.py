#!/usr/bin/env python3
"""
اختبار جودة التحويل 100%
"""

import os
import sys
from PIL import Image

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.image_to_map import ImageToMapConverter
from src.map_to_image import MapToImageConverter

def create_test_image():
    """إنشاء صورة تجريبية بسيطة"""
    
    # إنشاء صورة 10x10 بألوان محددة
    img = Image.new("RGBA", (10, 10), (255, 255, 255, 255))
    pixels = img.load()
    
    # رسم نمط بسيط
    colors = [
        (255, 0, 0, 255),    # أحمر
        (0, 255, 0, 255),    # أخضر  
        (0, 0, 255, 255),    # أزرق
        (255, 255, 0, 255),  # أصفر
    ]
    
    for y in range(10):
        for x in range(10):
            color_index = (x + y) % len(colors)
            pixels[x, y] = colors[color_index]
    
    os.makedirs("test_quality", exist_ok=True)
    img.save("test_quality/original.png")
    
    print("✅ تم إنشاء الصورة التجريبية: test_quality/original.png")
    return "test_quality/original.png"

def test_100_percent_quality():
    """اختبار جودة 100%"""
    
    print("🧪 اختبار جودة 100% (بدون ضغط)")
    print("=" * 50)
    
    # إنشاء صورة تجريبية
    original_path = create_test_image()
    
    # تحويل إلى خريطة (جودة 100%)
    print("\n📷 تحويل الصورة إلى خريطة...")
    converter = ImageToMapConverter()
    map_path = "test_quality/map_100_percent.json"
    
    success = converter.convert_image_to_map(
        image_path=original_path,
        output_path=map_path,
        compress_colors=False,  # بدون ضغط
        skip_transparent=False  # تضمين جميع البكسلات
    )
    
    if not success:
        print("❌ فشل في تحويل الصورة إلى خريطة")
        return False
    
    # إعادة بناء الصورة
    print("\n🖼️  إعادة بناء الصورة من الخريطة...")
    map_converter = MapToImageConverter()
    rebuilt_path = "test_quality/rebuilt_100_percent.png"
    
    success = map_converter.convert_map_to_image(
        map_path=map_path,
        output_path=rebuilt_path
    )
    
    if not success:
        print("❌ فشل في إعادة بناء الصورة")
        return False
    
    # مقارنة الصور
    print("\n🔍 مقارنة الصور...")
    return compare_images(original_path, rebuilt_path)

def compare_images(original_path, rebuilt_path):
    """مقارنة الصورتين بكسل بكسل"""
    
    try:
        # فتح الصورتين
        original = Image.open(original_path).convert("RGBA")
        rebuilt = Image.open(rebuilt_path).convert("RGBA")
        
        # التحقق من الأبعاد
        if original.size != rebuilt.size:
            print(f"❌ الأبعاد مختلفة:")
            print(f"   الأصلية: {original.size}")
            print(f"   المُعادة: {rebuilt.size}")
            return False
        
        # مقارنة البكسلات
        original_pixels = original.load()
        rebuilt_pixels = rebuilt.load()
        
        width, height = original.size
        differences = 0
        
        for y in range(height):
            for x in range(width):
                original_pixel = original_pixels[x, y]
                rebuilt_pixel = rebuilt_pixels[x, y]
                
                if original_pixel != rebuilt_pixel:
                    differences += 1
                    print(f"⚠️  اختلاف في ({x}, {y}):")
                    print(f"   الأصلي: {original_pixel}")
                    print(f"   المُعاد: {rebuilt_pixel}")
        
        if differences == 0:
            print("✅ الصور متطابقة تماماً - جودة 100%!")
            print(f"   تم فحص {width * height} بكسل")
            return True
        else:
            print(f"❌ وجد {differences} اختلاف من أصل {width * height} بكسل")
            accuracy = ((width * height - differences) / (width * height)) * 100
            print(f"   دقة التطابق: {accuracy:.2f}%")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في المقارنة: {e}")
        return False

def test_file_sizes():
    """اختبار أحجام الملفات"""
    
    print("\n📊 مقارنة أحجام الملفات:")
    print("-" * 30)
    
    files = [
        ("الصورة الأصلية", "test_quality/original.png"),
        ("خريطة البكسلات", "test_quality/map_100_percent.json"),
        ("الصورة المُعادة", "test_quality/rebuilt_100_percent.png")
    ]
    
    for name, path in files:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"   {name}: {size:,} بايت ({size/1024:.2f} KB)")
        else:
            print(f"   {name}: ملف غير موجود")

def main():
    """الدالة الرئيسية"""
    
    print("🔥 اختبار جودة 100% - Pixel Map Converter")
    print("=" * 60)
    
    # تشغيل اختبار الجودة
    success = test_100_percent_quality()
    
    # عرض أحجام الملفات
    test_file_sizes()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if success:
        print("🎉 النتيجة: اختبار الجودة 100% نجح!")
        print("✅ الصورة المُعادة مطابقة تماماً للأصلية")
    else:
        print("❌ النتيجة: اختبار الجودة فشل!")
        print("⚠️  هناك اختلافات بين الصورة الأصلية والمُعادة")
    
    print(f"\n📁 الملفات محفوظة في: test_quality/")

if __name__ == "__main__":
    main()
