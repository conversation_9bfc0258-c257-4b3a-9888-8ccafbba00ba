<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Title -->
    <title>تشفير الصور الآمن - أقوى طريقة لتشفير الصور بمفتاح AES-256 | Image Encryption</title>

    <!-- SEO Meta Description -->
    <meta name="description" content="أقوى موقع لتشفير الصور بشكل آمن باستخدام مفتاح خاص وتقنية AES-256-GCM. تشفير صورة عبر مفتاح، حماية الصور، تشفير قوي للصور. Secure image encryption with private key and AES-256-GCM technology.">

    <!-- SEO Keywords -->
    <meta name="keywords" content="تشفير صورة, تشفير صورة عبر مفتاح, تشفير صورة بشكل قوي, اقوى طريقة لتشفير صورة, تشفير صورة AES-256-GCM, تشفير صورة بمفتاح, حماية الصور, تشفير الصور الآمن, تشفير محلي للصور, فك تشفير الصور, image encryption, encrypt image with key, secure image encryption, AES-256-GCM image encryption, strong image encryption, image protection, local image encryption, private key encryption, decrypt images, image security">

    <!-- Author -->
    <meta name="author" content="موقع تشفير الصور الآمن - Secure Image Encryption">

    <!-- Robots -->
    <meta name="robots" content="index, follow">

    <!-- Language -->
    <meta name="language" content="Arabic, English">

    <!-- Additional SEO Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="msapplication-TileColor" content="#667eea">
    <meta name="application-name" content="تشفير الصور الآمن">
    <meta name="apple-mobile-web-app-title" content="تشفير الصور">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="تشفير الصور الآمن - أقوى طريقة لتشفير الصور بمفتاح AES-256">
    <meta property="og:description" content="أقوى موقع لتشفير الصور بشكل آمن باستخدام مفتاح خاص وتقنية AES-256-GCM. تشفير صورة عبر مفتاح، حماية الصور، تشفير قوي للصور.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://fiugaewipfgipwagif.web.app">
    <meta property="og:site_name" content="موقع تشفير الصور الآمن">
    <meta property="og:locale" content="ar_SA">
    <meta property="og:locale:alternate" content="en_US">
    <meta property="og:image" content="https://fiugaewipfgipwagif.web.app/assets/og-image.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="تشفير الصور الآمن - أقوى طريقة لتشفير الصور">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="تشفير الصور الآمن - أقوى طريقة لتشفير الصور بمفتاح AES-256">
    <meta name="twitter:description" content="أقوى موقع لتشفير الصور بشكل آمن باستخدام مفتاح خاص وتقنية AES-256-GCM. تشفير صورة عبر مفتاح، حماية الصور، تشفير قوي للصور.">
    <meta name="twitter:image" content="https://fiugaewipfgipwagif.web.app/assets/og-image.png">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://fvslygdluy.web.app">

    <!-- Alternate Languages -->
    <link rel="alternate" hreflang="ar" href="https://fiugaewipfgipwagif.web.app">
    <link rel="alternate" hreflang="en" href="https://fiugaewipfgipwagif.web.app">
    <link rel="alternate" hreflang="x-default" href="https://fiugaewipfgipwagif.web.app">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon.ico">
    <link rel="apple-touch-icon" href="/assets/apple-touch-icon.png">

    <!-- Schema.org Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "تشفير الصور الآمن - Secure Image Encryption",
        "description": "أقوى موقع لتشفير الصور بشكل آمن باستخدام مفتاح خاص وتقنية AES-256-GCM. تشفير صورة عبر مفتاح، حماية الصور، تشفير قوي للصور.",
        "url": "https://fvslygdluy.web.app",
        "applicationCategory": "SecurityApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "author": {
            "@type": "Organization",
            "name": "موقع تشفير الصور الآمن"
        },
        "keywords": "تشفير صورة, تشفير صورة عبر مفتاح, تشفير صورة بشكل قوي, اقوى طريقة لتشفير صورة, تشفير صورة AES-256-GCM, image encryption, encrypt image with key, secure image encryption",
        "inLanguage": ["ar", "en"],
        "featureList": [
            "تشفير الصور بمفتاح خاص",
            "تقنية AES-256-GCM",
            "تشفير محلي آمن",
            "دعم متعدد اللغات",
            "واجهة سهلة الاستخدام",
            "حماية كاملة للخصوصية"
        ]
    }
    </script>

    <!-- CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar {
            padding: 1rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .nav-logo h1 {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 0.2rem;
        }

        .nav-logo span {
            color: #666;
            font-size: 0.9rem;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
        }

        .nav-link {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: #667eea;
        }

        .language-toggle, .dark-mode-toggle {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin-left: 0.5rem;
        }

        .language-toggle {
            background: #4CAF50;
            font-size: 14px;
        }

        .language-toggle:hover {
            background: #45a049;
            transform: scale(1.1);
        }

        .dark-mode-toggle:hover {
            background: #5a6fd8;
            transform: scale(1.1);
        }

        .main-content {
            margin-top: 80px;
        }

        .section {
            padding: 4rem 0;
            margin: 2rem 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 6rem 0;
        }

        .hero-title {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .hero-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.1rem;
        }

        .feature-icon {
            font-size: 1.5rem;
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn-primary { background: linear-gradient(135deg, #2196F3, #1976D2); }
        .btn-secondary { background: linear-gradient(135deg, #9E9E9E, #757575); }
        .btn-success { background: linear-gradient(135deg, #4CAF50, #388E3C); }
        .btn-warning { background: linear-gradient(135deg, #FF9800, #F57C00); }
        .btn-danger { background: linear-gradient(135deg, #F44336, #D32F2F); }
        .btn-secure { background: linear-gradient(135deg, #9C27B0, #7B1FA2); }
        .btn-info { background: linear-gradient(135deg, #00BCD4, #0097A7); }

        .key-section {
            background: white;
            border-radius: 20px;
            margin: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .security-info {
            background: linear-gradient(135deg, #E8F5E8, #C8E6C9);
            border: 2px solid #4CAF50;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .security-info h3 {
            color: #2E7D32;
            margin-bottom: 1rem;
        }

        .security-info ul {
            list-style: none;
            color: #2E7D32;
        }

        .key-actions {
            display: flex;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }

        .key-limit-info {
            background: linear-gradient(135deg, #fff3e0, #ffcc80);
            border: 2px solid #ff9800;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }

        .key-limit-info p {
            margin: 0.5rem 0;
            color: #e65100;
            font-weight: bold;
        }

        #keyCount {
            color: #d84315;
            font-size: 1.1em;
        }

        .key-display {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            border: 2px solid #e9ecef;
        }

        .key-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 1rem 0;
        }

        .key-text {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            flex: 1;
        }

        .copy-btn {
            background: #34495e;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .key-info {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }

        .fingerprint, .key-length {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 5px 10px;
            border-radius: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .warning-box {
            background: linear-gradient(135deg, #FFF3E0, #FFCC80);
            border: 2px solid #FF9800;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            color: #E65100;
            font-weight: bold;
        }

        .input-field, .select-field {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            margin: 10px 0;
        }

        .input-field:focus, .select-field:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 3rem;
            text-align: center;
            margin: 2rem 0;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .file-input {
            display: none;
        }

        .file-label {
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
        }

        .upload-hint {
            color: #666;
            font-size: 0.9rem;
        }

        .preview-area {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .preview-box {
            text-align: center;
        }

        .preview-box h3 {
            margin-bottom: 1rem;
            color: #333;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            max-width: 100%;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .info-box {
            background: #e3f2fd;
            border: 2px solid #2196F3;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }

        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .footer-section h3, .footer-section h4 {
            margin-bottom: 1rem;
            color: #ecf0f1;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section a {
            color: #bdc3c7;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: #ecf0f1;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            margin-top: 2rem;
            border-top: 1px solid #34495e;
            color: #bdc3c7;
        }

        .option-group {
            margin: 1rem 0;
        }

        .option-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }

        .option-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
        }

        .tab-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .files-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .files-count {
            background: #e9ecef;
            padding: 8px 15px;
            border-radius: 20px;
            color: #495057;
            font-weight: bold;
        }

        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }

        .file-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .file-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .file-info h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .file-info p {
            color: #666;
            margin: 0.25rem 0;
            font-size: 0.9rem;
        }

        .file-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .file-actions .btn {
            flex: 1;
            padding: 8px 12px;
            font-size: 14px;
        }

        .cloud-files {
            max-height: 400px;
            overflow-y: auto;
        }

        .cloud-file-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cloud-file-item:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .file-icon {
            font-size: 2rem;
        }

        .file-details {
            flex: 1;
        }

        .file-details h4 {
            margin-bottom: 0.25rem;
            color: #333;
        }

        .file-details p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .file-select {
            font-size: 1.5rem;
            color: #667eea;
        }

        .existing-key {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            border: 2px solid #e9ecef;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }

        .encryption-options {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            border: 2px solid #e9ecef;
        }

        .decrypt-options {
            margin: 2rem 0;
        }

        .about-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .about-box {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .about-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .about-box h3 {
            color: #667eea;
            margin-bottom: 1rem;
        }

        .about-box p {
            color: #666;
            line-height: 1.6;
        }

        .support-box {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border: 2px solid #4CAF50;
        }

        .support-contact {
            text-align: center;
            margin-top: 1rem;
        }

        .support-email {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .support-email:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .support-note {
            margin-top: 1rem;
            color: #2E7D32;
            font-style: italic;
            font-weight: bold;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #e0e0e0;
        }

        body.dark-mode .header {
            background: rgba(26, 26, 46, 0.95);
        }

        body.dark-mode .nav-logo h1 {
            color: #64b5f6;
        }

        body.dark-mode .nav-logo span {
            color: #b0b0b0;
        }

        body.dark-mode .nav-link {
            color: #e0e0e0;
        }

        body.dark-mode .nav-link:hover {
            color: #64b5f6;
        }

        body.dark-mode .dark-mode-toggle {
            background: #ffa726;
        }

        body.dark-mode .dark-mode-toggle:hover {
            background: #ff9800;
        }

        body.dark-mode .key-section,
        body.dark-mode .about-box,
        body.dark-mode .file-card {
            background: #2d2d44;
            border-color: #404040;
        }

        body.dark-mode .security-info {
            background: linear-gradient(135deg, #1b5e20, #2e7d32);
            border-color: #4caf50;
        }

        body.dark-mode .security-info h3,
        body.dark-mode .security-info ul {
            color: #a5d6a7;
        }

        body.dark-mode .key-display {
            background: #2d2d44;
            border-color: #404040;
        }

        body.dark-mode .key-text {
            background: #1a1a2e;
            color: #e0e0e0;
        }

        body.dark-mode .warning-box {
            background: linear-gradient(135deg, #3e2723, #5d4037);
            border-color: #ff9800;
            color: #ffcc80;
        }

        body.dark-mode .input-field,
        body.dark-mode .select-field {
            background: #2d2d44;
            border-color: #404040;
            color: #e0e0e0;
        }

        body.dark-mode .input-field:focus,
        body.dark-mode .select-field:focus {
            border-color: #64b5f6;
        }

        body.dark-mode .upload-area {
            border-color: #404040;
            background: rgba(45, 45, 68, 0.3);
        }

        body.dark-mode .upload-area:hover {
            border-color: #64b5f6;
            background: rgba(100, 181, 246, 0.1);
        }

        body.dark-mode .upload-icon {
            color: #64b5f6;
        }

        body.dark-mode .upload-hint {
            color: #b0b0b0;
        }

        body.dark-mode .preview-box h3,
        body.dark-mode .about-box h3 {
            color: #64b5f6;
        }

        body.dark-mode .about-box p,
        body.dark-mode .file-info p {
            color: #b0b0b0;
        }

        body.dark-mode canvas {
            border-color: #404040;
        }

        body.dark-mode .info-box {
            background: #1a1a2e;
            border-color: #64b5f6;
            color: #e0e0e0;
        }

        body.dark-mode .footer {
            background: #1a1a2e;
        }

        body.dark-mode .encryption-options,
        body.dark-mode .existing-key {
            background: #2d2d44;
            border-color: #404040;
        }

        body.dark-mode .option-group label {
            color: #e0e0e0;
        }

        body.dark-mode .support-box {
            background: linear-gradient(135deg, #2d4a2d, #1e3a1e);
            border-color: #4CAF50;
        }

        body.dark-mode .support-email {
            background: #4CAF50;
            color: white;
        }

        body.dark-mode .support-email:hover {
            background: #45a049;
        }

        body.dark-mode .support-note {
            color: #81c784;
        }

        body.dark-mode .language-toggle {
            background: #4CAF50;
            color: white;
        }

        body.dark-mode .language-toggle:hover {
            background: #45a049;
        }



        .section-description {
            text-align: center;
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        .pdf-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            justify-content: center;
        }

        .pdf-tabs .tab-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            font-weight: bold;
        }

        .pdf-tabs .tab-btn.active {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            border-color: #FF6B35;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .pdf-tabs .tab-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .pdf-info {
            margin: 2rem 0;
        }

        .pdf-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-box {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .feature-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #FF6B35;
        }

        .feature-box h3 {
            color: #FF6B35;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .feature-box p {
            color: #666;
            line-height: 1.6;
        }



        body.dark-mode .section-description {
            color: #e0e0e0;
        }

        body.dark-mode .pdf-tabs .tab-btn {
            background: #404040;
            border-color: #555;
            color: #e0e0e0;
        }

        body.dark-mode .pdf-tabs .tab-btn.active {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            border-color: #FF6B35;
        }

        body.dark-mode .feature-box {
            background: linear-gradient(135deg, #404040, #555);
            border-color: #666;
        }

        body.dark-mode .feature-box:hover {
            border-color: #FF6B35;
        }

        body.dark-mode .feature-box h3 {
            color: #FF6B35;
        }

        body.dark-mode .feature-box p {
            color: #e0e0e0;
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
            }

            .hero-title {
                font-size: 2rem;
            }

            .hero-features {
                grid-template-columns: 1fr;
            }

            .hero-actions {
                flex-direction: column;
                align-items: center;
            }

            .key-actions {
                flex-direction: column;
            }

            .key-container {
                flex-direction: column;
            }

            .files-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .files-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .option-tabs {
                flex-direction: column;
            }

            .cloud-file-item {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>

    <!-- Firebase -->
    <script type="module" src="/firebase-config.js"></script>

    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J12L13XPHZ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-J12L13XPHZ');
    </script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h1>🔐 Image Encryption</h1>
                    <!-- Clean header without domain name -->
                </div>
                <div class="nav-menu">
                    <a href="#home" class="nav-link">الرئيسية</a>
                    <a href="#encrypt" class="nav-link">تشفير</a>
                    <a href="#decrypt" class="nav-link">فك التشفير</a>
                    <a href="#about" class="nav-link">حول</a>
                    <button class="language-toggle" onclick="toggleLanguage()">🌐 EN</button>
                    <button class="dark-mode-toggle" onclick="toggleDarkMode()">🌙</button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section id="home" class="hero">
            <div class="hero-container">
                <h1 class="hero-title">🔀 تخريب الصور بالمفتاح - المفتاح يحفظ مكان كل سطر في الصورة</h1>
                <p class="hero-subtitle">تحويل الصورة إلى JSON | المفتاح يخرب ترتيب الأسطر | إعادة ترتيب الأسطر بالمفتاح الصحيح</p>
                <div class="hero-features">
                    <div class="feature">
                        <span class="feature-icon">🔑</span>
                        <span>مفاتيح خاصة 50 حرف</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">🔀</span>
                        <span>تخريب ترتيب الأسطر</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">📄</span>
                        <span>تحويل إلى JSON</span>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn btn-primary" onclick="scrollToSection('encrypt')">
                        🔐 ابدأ التشفير
                    </button>
                    <button class="btn btn-secondary" onclick="generateNewKey()">
                        🔑 إنشاء مفتاح
                    </button>
                </div>
            </div>
        </section>

        <!-- Key Management Section -->
        <section id="key-section" class="section key-section">
            <div class="container">
                <h2>🔑 إدارة المفتاح الخاص - مفتاح تشفير AES-256 آمن</h2>

                <div class="security-info">
                    <h3>🛡️ الأمان:</h3>
                    <ul>
                        <li>• مفتاح خاص 50 حرف</li>
                        <li>• تشفير AES متقدم</li>
                        <li>• تخزين آمن</li>
                        <li>• حماية كاملة</li>
                    </ul>
                </div>

                <div class="key-actions">
                    <button class="btn btn-secure" onclick="generateNewKey()">
                        🔐 إنشاء مفتاح خاص جديد
                    </button>
                    <button class="btn btn-info" onclick="loadExistingKey()">
                        📥 استخدام مفتاح موجود
                    </button>
                </div>

                <div class="key-limit-info">
                    <p>⏱️ يمكنك إنشاء مفتاحين فقط في الدقيقة الواحدة</p>
                    <p id="keyCounter">المفاتيح المُنشأة: <span id="keyCount">0</span>/2</p>
                </div>

                <div id="keyDisplay" class="key-display" style="display: none;">
                    <h3>🔑 مفتاحك الخاص:</h3>
                    <div class="key-container">
                        <div class="key-text" id="generatedKey"></div>
                        <button class="copy-btn" onclick="copyKey()">📋 نسخ</button>
                    </div>
                    <div class="key-info">
                        <span class="fingerprint" id="keyFingerprint"></span>
                        <span class="key-length" id="keyLength"></span>
                    </div>

                    <div class="warning-box">
                        ⚠️ احفظ هذا المفتاح في مكان آمن! لن تتمكن من استرجاع صورك المشفرة بدونه!
                    </div>
                </div>

                <div id="existingKeyInput" class="existing-key" style="display: none;">
                    <h3>📥 أدخل مفتاحك الموجود:</h3>
                    <input type="text" id="userKey" class="input-field"
                           placeholder="أدخل مفتاحك الخاص هنا (50 حرف)">
                    <button class="btn btn-primary" onclick="validateAndSetKey()">
                        ✅ تأكيد المفتاح
                    </button>
                </div>
            </div>
        </section>

        <!-- Encryption Section -->
        <section id="encrypt" class="section encrypt-section">
            <div class="container">
                <h2>🔀 تخريب الصورة - المفتاح يحفظ مكان كل سطر ويخرب ترتيبها</h2>

                <div class="upload-area">
                    <input type="file" id="imageInput" class="file-input" accept="image/*">
                    <label for="imageInput" class="file-label">
                        <span class="upload-icon">📁</span>
                        <span>اختيار صورة للتخريب</span>
                        <span class="upload-hint">PNG, JPG, JPEG, BMP</span>
                    </label>
                </div>

                <div class="encryption-options">
                    <div class="option-group">
                        <label>🔐 مستوى التشفير:</label>
                        <select id="encryptionLevel" class="select-field">
                            <option value="standard">🔒 قياسي (سريع)</option>
                            <option value="high" selected>🛡️ عالي (موصى به)</option>
                            <option value="maximum">🔐 أقصى (أبطأ)</option>
                        </select>
                    </div>


                </div>

                <div class="action-buttons">
                    <button class="btn btn-warning" onclick="encryptImage()" id="encryptBtn" disabled>
                        🔀 تخريب وتحويل
                    </button>
                    <button class="btn btn-success" onclick="downloadEncryptedMap()" id="downloadEncryptedBtn" disabled>
                        💾 تحميل ملف JSON
                    </button>
                </div>

                <div class="preview-area">
                    <div class="preview-box">
                        <h3>الصورة الأصلية</h3>
                        <canvas id="originalCanvas" width="300" height="300"></canvas>
                    </div>
                    <div class="preview-box">
                        <h3>معاينة مخربة</h3>
                        <canvas id="encryptedPreview" width="300" height="300"></canvas>
                    </div>
                </div>

                <div class="info-box" id="encryptionInfo">
                    اختر صورة وتأكد من وجود مفتاحك لبدء التشفير...
                </div>
            </div>
        </section>

        <!-- Decryption Section -->
        <section id="decrypt" class="section decrypt-section">
            <div class="container">
                <h2>🔓 فك تشفير الصور - استعادة الصورة الأصلية بمفتاح AES-256</h2>

                <div class="upload-area">
                    <input type="file" id="encryptedMapInput" class="file-input" accept=".json">
                    <label for="encryptedMapInput" class="file-label">
                        <span class="upload-icon">📁</span>
                        <span>اختيار خريطة مشفرة</span>
                        <span class="upload-hint">ملفات JSON فقط</span>
                    </label>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-danger" onclick="decryptMap()" id="decryptBtn" disabled>
                        🔓 فك التشفير وإعادة البناء
                    </button>
                    <button class="btn btn-success" onclick="downloadDecryptedImage()" id="downloadDecryptedBtn" disabled>
                        💾 تحميل الصورة
                    </button>
                </div>

                <div class="preview-area">
                    <div class="preview-box">
                        <h3>الصورة المُعادة</h3>
                        <canvas id="decryptedCanvas" width="300" height="300"></canvas>
                    </div>
                </div>

                <div class="info-box" id="decryptionInfo">
                    اختر خريطة مشفرة وتأكد من صحة مفتاحك لفك التشفير...
                </div>
            </div>
        </section>



        <!-- About Section -->
        <section id="about" class="section about-section">
            <div class="container">
                <h2>📖 حول موقع تشفير الصور الآمن - أقوى طريقة لتشفير الصور بمفتاح AES-256</h2>

                <div class="about-content">
                    <div class="about-box">
                        <h3>🔐 تشفير صورة بشكل قوي - تقنية AES-256-GCM</h3>
                        <p>أقوى طريقة لتشفير الصور باستخدام تقنية AES-256-GCM المتقدمة. جميع عمليات تشفير الصور تتم في متصفحك مباشرة بدون إرسال أي بيانات للخوادم. تشفير صورة عبر مفتاح خاص آمن 100%.</p>
                    </div>

                    <div class="about-box">
                        <h3>🔑 مفاتيحك ملكك</h3>
                        <p>أنت الوحيد الذي يملك مفتاح التشفير. لا أحد يمكنه الوصول لصورك بدونه.</p>
                    </div>

                    <div class="about-box">
                        <h3>💻 يعمل بدون إنترنت</h3>
                        <p>بعد تحميل الصفحة، يمكنك استخدام الموقع بدون اتصال إنترنت.</p>
                    </div>

                    <div class="about-box support-box">
                        <h3>📧 الدعم والمساعدة</h3>
                        <p>هل تحتاج مساعدة أو لديك استفسار؟</p>
                        <div class="support-contact">
                            <a href="mailto:<EMAIL>" class="support-email">
                                📧 <EMAIL>
                            </a>
                            <p class="support-note">نحن هنا لمساعدتك في أي وقت!</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-section">
                <h3>🔐 Image Encryption</h3>
                <p>موقع تشفير الصور الآمن</p>
            </div>
            <div class="footer-section">
                <h4>الخدمات</h4>
                <ul>
                    <li><a href="#encrypt">تشفير الصور</a></li>
                    <li><a href="#decrypt">فك التشفير</a></li>
                    <li><a href="#about">حول الموقع</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h4>الأمان</h4>
                <ul>
                    <li>تشفير AES متقدم</li>
                    <li>مفاتيح خاصة آمنة</li>
                    <li>تشفير محلي آمن</li>
                </ul>
            </div>
        </div>
        <div class="footer-bottom">
            <p>موقع تشفير الصور الآمن</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/scripts/encryption.js"></script>
    <script src="/scripts/main.js"></script>
</body>
</html>
