<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Image Encryption - تشفير الصور الآمن</title>
    <meta name="description" content="موقع تشفير الصور الآمن - حول صورك إلى خرائط بكسلات مشفرة بمفاتيح خاصة">
    <meta name="keywords" content="تشفير الصور, حماية الصور, خريطة البكسلات, أمان الصور">
    <meta name="author" content="Image Encryption">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Image Encryption - تشفير الصور الآمن">
    <meta property="og:description" content="حول صورك إلى خرائط بكسلات مشفرة بأمان عالي">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://imageencryption.com">
    <meta property="og:image" content="https://imageencryption.com/assets/og-image.png">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon.ico">
    <link rel="apple-touch-icon" href="/assets/apple-touch-icon.png">

    <!-- CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar {
            padding: 1rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .nav-logo h1 {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 0.2rem;
        }

        .nav-logo span {
            color: #666;
            font-size: 0.9rem;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
        }

        .nav-link {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: #667eea;
        }

        .main-content {
            margin-top: 80px;
        }

        .section {
            padding: 4rem 0;
            margin: 2rem 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 6rem 0;
        }

        .hero-title {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .hero-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.1rem;
        }

        .feature-icon {
            font-size: 1.5rem;
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn-primary { background: linear-gradient(135deg, #2196F3, #1976D2); }
        .btn-secondary { background: linear-gradient(135deg, #9E9E9E, #757575); }
        .btn-success { background: linear-gradient(135deg, #4CAF50, #388E3C); }
        .btn-warning { background: linear-gradient(135deg, #FF9800, #F57C00); }
        .btn-danger { background: linear-gradient(135deg, #F44336, #D32F2F); }
        .btn-secure { background: linear-gradient(135deg, #9C27B0, #7B1FA2); }
        .btn-info { background: linear-gradient(135deg, #00BCD4, #0097A7); }

        .key-section {
            background: white;
            border-radius: 20px;
            margin: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .security-info {
            background: linear-gradient(135deg, #E8F5E8, #C8E6C9);
            border: 2px solid #4CAF50;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .security-info h3 {
            color: #2E7D32;
            margin-bottom: 1rem;
        }

        .security-info ul {
            list-style: none;
            color: #2E7D32;
        }

        .key-actions {
            display: flex;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }

        .key-display {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            border: 2px solid #e9ecef;
        }

        .key-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 1rem 0;
        }

        .key-text {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            flex: 1;
        }

        .copy-btn {
            background: #34495e;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .key-info {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }

        .fingerprint, .key-length {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 5px 10px;
            border-radius: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .warning-box {
            background: linear-gradient(135deg, #FFF3E0, #FFCC80);
            border: 2px solid #FF9800;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            color: #E65100;
            font-weight: bold;
        }

        .input-field, .select-field {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            margin: 10px 0;
        }

        .input-field:focus, .select-field:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 3rem;
            text-align: center;
            margin: 2rem 0;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .file-input {
            display: none;
        }

        .file-label {
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
        }

        .upload-hint {
            color: #666;
            font-size: 0.9rem;
        }

        .preview-area {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .preview-box {
            text-align: center;
        }

        .preview-box h3 {
            margin-bottom: 1rem;
            color: #333;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            max-width: 100%;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .info-box {
            background: #e3f2fd;
            border: 2px solid #2196F3;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }

        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .footer-section h3, .footer-section h4 {
            margin-bottom: 1rem;
            color: #ecf0f1;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section a {
            color: #bdc3c7;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: #ecf0f1;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            margin-top: 2rem;
            border-top: 1px solid #34495e;
            color: #bdc3c7;
        }

        .option-group {
            margin: 1rem 0;
        }

        .option-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }

        .option-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
        }

        .tab-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .files-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .files-count {
            background: #e9ecef;
            padding: 8px 15px;
            border-radius: 20px;
            color: #495057;
            font-weight: bold;
        }

        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }

        .file-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .file-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .file-info h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .file-info p {
            color: #666;
            margin: 0.25rem 0;
            font-size: 0.9rem;
        }

        .file-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .file-actions .btn {
            flex: 1;
            padding: 8px 12px;
            font-size: 14px;
        }

        .cloud-files {
            max-height: 400px;
            overflow-y: auto;
        }

        .cloud-file-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cloud-file-item:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .file-icon {
            font-size: 2rem;
        }

        .file-details {
            flex: 1;
        }

        .file-details h4 {
            margin-bottom: 0.25rem;
            color: #333;
        }

        .file-details p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .file-select {
            font-size: 1.5rem;
            color: #667eea;
        }

        .existing-key {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            border: 2px solid #e9ecef;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }

        .encryption-options {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            border: 2px solid #e9ecef;
        }

        .decrypt-options {
            margin: 2rem 0;
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
            }

            .hero-title {
                font-size: 2rem;
            }

            .hero-features {
                grid-template-columns: 1fr;
            }

            .hero-actions {
                flex-direction: column;
                align-items: center;
            }

            .key-actions {
                flex-direction: column;
            }

            .key-container {
                flex-direction: column;
            }

            .files-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .files-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .option-tabs {
                flex-direction: column;
            }

            .cloud-file-item {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>

    <!-- Firebase -->
    <script type="module" src="/firebase-config.js"></script>

    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J12L13XPHZ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-J12L13XPHZ');
    </script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h1>🔐 Image Encryption</h1>
                    <span>imageencryption.com</span>
                </div>
                <div class="nav-menu">
                    <a href="#home" class="nav-link">الرئيسية</a>
                    <a href="#encrypt" class="nav-link">تشفير</a>
                    <a href="#decrypt" class="nav-link">فك التشفير</a>
                    <a href="#files" class="nav-link">ملفاتي</a>
                    <a href="#about" class="nav-link">حول</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section id="home" class="hero">
            <div class="hero-container">
                <h1 class="hero-title">🔐 تشفير الصور الآمن</h1>
                <p class="hero-subtitle">حول صورك إلى خرائط بكسلات مشفرة بأمان عالي</p>
                <div class="hero-features">
                    <div class="feature">
                        <span class="feature-icon">🔑</span>
                        <span>مفاتيح خاصة 50 حرف</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">🛡️</span>
                        <span>تشفير متقدم AES</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">☁️</span>
                        <span>تخزين سحابي آمن</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">🌐</span>
                        <span>يعمل في المتصفح</span>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn btn-primary" onclick="scrollToSection('encrypt')">
                        🔐 ابدأ التشفير
                    </button>
                    <button class="btn btn-secondary" onclick="generateNewKey()">
                        🔑 إنشاء مفتاح
                    </button>
                </div>
            </div>
        </section>

        <!-- Key Management Section -->
        <section id="key-section" class="section key-section">
            <div class="container">
                <h2>🔑 إدارة المفتاح الخاص</h2>

                <div class="security-info">
                    <h3>🛡️ الأمان المتقدم:</h3>
                    <ul>
                        <li>• كل مستخدم يحصل على مفتاح خاص بطول 50 حرف</li>
                        <li>• التشفير باستخدام خوارزمية AES المتقدمة</li>
                        <li>• تخزين آمن في Firebase Cloud</li>
                        <li>• لا يمكن لأي شخص فتح صورك بدون مفتاحك</li>
                    </ul>
                </div>

                <div class="key-actions">
                    <button class="btn btn-secure" onclick="generateNewKey()">
                        🔐 إنشاء مفتاح خاص جديد
                    </button>
                    <button class="btn btn-info" onclick="loadExistingKey()">
                        📥 استخدام مفتاح موجود
                    </button>
                </div>

                <div id="keyDisplay" class="key-display" style="display: none;">
                    <h3>🔑 مفتاحك الخاص:</h3>
                    <div class="key-container">
                        <div class="key-text" id="generatedKey"></div>
                        <button class="copy-btn" onclick="copyKey()">📋 نسخ</button>
                    </div>
                    <div class="key-info">
                        <span class="fingerprint" id="keyFingerprint"></span>
                        <span class="key-length" id="keyLength"></span>
                    </div>

                    <div class="warning-box">
                        ⚠️ احفظ هذا المفتاح في مكان آمن! لن تتمكن من استرجاع صورك المشفرة بدونه!
                    </div>
                </div>

                <div id="existingKeyInput" class="existing-key" style="display: none;">
                    <h3>📥 أدخل مفتاحك الموجود:</h3>
                    <input type="text" id="userKey" class="input-field"
                           placeholder="أدخل مفتاحك الخاص هنا (50 حرف)">
                    <button class="btn btn-primary" onclick="validateAndSetKey()">
                        ✅ تأكيد المفتاح
                    </button>
                </div>
            </div>
        </section>

        <!-- Encryption Section -->
        <section id="encrypt" class="section encrypt-section">
            <div class="container">
                <h2>🔒 تشفير صورة إلى خريطة آمنة</h2>

                <div class="upload-area">
                    <input type="file" id="imageInput" class="file-input" accept="image/*">
                    <label for="imageInput" class="file-label">
                        <span class="upload-icon">📁</span>
                        <span>اختيار صورة للتشفير</span>
                        <span class="upload-hint">PNG, JPG, JPEG, BMP</span>
                    </label>
                </div>

                <div class="encryption-options">
                    <div class="option-group">
                        <label>🔐 مستوى التشفير:</label>
                        <select id="encryptionLevel" class="select-field">
                            <option value="standard">🔒 قياسي (سريع)</option>
                            <option value="high" selected>🛡️ عالي (موصى به)</option>
                            <option value="maximum">🔐 أقصى (أبطأ)</option>
                        </select>
                    </div>

                    <div class="option-group">
                        <label>☁️ حفظ في السحابة:</label>
                        <input type="checkbox" id="saveToCloud" checked>
                        <span>حفظ نسخة في Firebase</span>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-warning" onclick="encryptImage()" id="encryptBtn" disabled>
                        🔐 تشفير وتحويل
                    </button>
                    <button class="btn btn-success" onclick="downloadEncryptedMap()" id="downloadEncryptedBtn" disabled>
                        💾 تحميل الخريطة المشفرة
                    </button>
                </div>

                <div class="preview-area">
                    <div class="preview-box">
                        <h3>الصورة الأصلية</h3>
                        <canvas id="originalCanvas" width="300" height="300"></canvas>
                    </div>
                    <div class="preview-box">
                        <h3>معاينة مشفرة</h3>
                        <canvas id="encryptedPreview" width="300" height="300"></canvas>
                    </div>
                </div>

                <div class="info-box" id="encryptionInfo">
                    اختر صورة وتأكد من وجود مفتاحك لبدء التشفير...
                </div>
            </div>
        </section>

        <!-- Decryption Section -->
        <section id="decrypt" class="section decrypt-section">
            <div class="container">
                <h2>🔓 فك تشفير خريطة إلى صورة</h2>

                <div class="decrypt-options">
                    <div class="option-tabs">
                        <button class="tab-btn active" onclick="switchTab('upload')">📁 رفع ملف</button>
                        <button class="tab-btn" onclick="switchTab('cloud')">☁️ من السحابة</button>
                    </div>
                </div>

                <div id="uploadTab" class="tab-content active">
                    <div class="upload-area">
                        <input type="file" id="encryptedMapInput" class="file-input" accept=".json">
                        <label for="encryptedMapInput" class="file-label">
                            <span class="upload-icon">📁</span>
                            <span>اختيار خريطة مشفرة</span>
                            <span class="upload-hint">ملفات JSON فقط</span>
                        </label>
                    </div>
                </div>

                <div id="cloudTab" class="tab-content">
                    <div class="cloud-files" id="cloudFilesList">
                        <p>جاري تحميل ملفاتك من السحابة...</p>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-danger" onclick="decryptMap()" id="decryptBtn" disabled>
                        🔓 فك التشفير وإعادة البناء
                    </button>
                    <button class="btn btn-success" onclick="downloadDecryptedImage()" id="downloadDecryptedBtn" disabled>
                        💾 تحميل الصورة
                    </button>
                </div>

                <div class="preview-area">
                    <div class="preview-box">
                        <h3>الصورة المُعادة</h3>
                        <canvas id="decryptedCanvas" width="300" height="300"></canvas>
                    </div>
                </div>

                <div class="info-box" id="decryptionInfo">
                    اختر خريطة مشفرة وتأكد من صحة مفتاحك لفك التشفير...
                </div>
            </div>
        </section>

        <!-- My Files Section -->
        <section id="files" class="section files-section">
            <div class="container">
                <h2>📁 ملفاتي المشفرة</h2>

                <div class="files-header">
                    <button class="btn btn-primary" onclick="refreshFilesList()">
                        🔄 تحديث القائمة
                    </button>
                    <span class="files-count" id="filesCount">0 ملف</span>
                </div>

                <div class="files-grid" id="filesGrid">
                    <p>لا توجد ملفات مشفرة بعد. ابدأ بتشفير صورة!</p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-section">
                <h3>🔐 Image Encryption</h3>
                <p>موقع تشفير الصور الآمن</p>
                <p>imageencryption.com</p>
            </div>
            <div class="footer-section">
                <h4>الخدمات</h4>
                <ul>
                    <li><a href="#encrypt">تشفير الصور</a></li>
                    <li><a href="#decrypt">فك التشفير</a></li>
                    <li><a href="#files">إدارة الملفات</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h4>الأمان</h4>
                <ul>
                    <li>تشفير AES متقدم</li>
                    <li>مفاتيح خاصة آمنة</li>
                    <li>تخزين سحابي محمي</li>
                </ul>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Image Encryption. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/scripts/encryption.js"></script>
    <script src="/scripts/ui.js"></script>
    <script src="/scripts/main.js"></script>
</body>
</html>
