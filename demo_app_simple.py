#!/usr/bin/env python3
"""
نسخة مبسطة من تطبيق Pixel Map Converter للعرض التوضيحي
تعمل بدون مكتبات خارجية - فقط Python المدمج
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
from PIL import Image, ImageTk
import threading

class SimplePixelMapApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🎨 Pixel Map Converter - عرض توضيحي")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # متغيرات التطبيق
        self.current_image_path = None
        self.current_map_path = None
        self.pixel_map_data = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2196F3', height=80)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame, 
            text="🎨 Pixel Map Converter - محول خريطة البكسلات",
            font=('Arial', 16, 'bold'),
            fg='white',
            bg='#2196F3'
        )
        title_label.pack(expand=True)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # قسم تحويل الصور
        self.create_image_to_map_section(main_frame)
        
        # قسم إعادة بناء الصور
        self.create_map_to_image_section(main_frame)
        
        # قسم المعلومات
        self.create_info_section(main_frame)
        
    def create_image_to_map_section(self, parent):
        """إنشاء قسم تحويل الصور"""
        frame = tk.LabelFrame(
            parent, 
            text="📷 تحويل صورة إلى خريطة بكسلات",
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#1976D2'
        )
        frame.pack(fill='x', pady=5)
        
        # أزرار التحكم
        btn_frame = tk.Frame(frame, bg='#f0f0f0')
        btn_frame.pack(fill='x', padx=10, pady=5)
        
        self.select_image_btn = tk.Button(
            btn_frame,
            text="📁 اختيار صورة",
            command=self.select_image,
            bg='#4CAF50',
            fg='white',
            font=('Arial', 10, 'bold'),
            width=15
        )
        self.select_image_btn.pack(side='left', padx=5)
        
        self.convert_to_map_btn = tk.Button(
            btn_frame,
            text="🔄 تحويل إلى خريطة",
            command=self.convert_image_to_map,
            bg='#FF9800',
            fg='white',
            font=('Arial', 10, 'bold'),
            width=15,
            state='disabled'
        )
        self.convert_to_map_btn.pack(side='left', padx=5)
        
        # معلومات الصورة
        self.image_info_label = tk.Label(
            frame,
            text="لم يتم اختيار صورة",
            bg='#f0f0f0',
            fg='#666'
        )
        self.image_info_label.pack(pady=5)
        
    def create_map_to_image_section(self, parent):
        """إنشاء قسم إعادة بناء الصور"""
        frame = tk.LabelFrame(
            parent,
            text="🗺️ تحويل خريطة إلى صورة",
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#1976D2'
        )
        frame.pack(fill='x', pady=5)
        
        # أزرار التحكم
        btn_frame = tk.Frame(frame, bg='#f0f0f0')
        btn_frame.pack(fill='x', padx=10, pady=5)
        
        self.select_map_btn = tk.Button(
            btn_frame,
            text="📁 اختيار خريطة",
            command=self.select_map,
            bg='#9C27B0',
            fg='white',
            font=('Arial', 10, 'bold'),
            width=15
        )
        self.select_map_btn.pack(side='left', padx=5)
        
        self.convert_to_image_btn = tk.Button(
            btn_frame,
            text="🖼️ إعادة بناء الصورة",
            command=self.convert_map_to_image,
            bg='#F44336',
            fg='white',
            font=('Arial', 10, 'bold'),
            width=15,
            state='disabled'
        )
        self.convert_to_image_btn.pack(side='left', padx=5)
        
        # معلومات الخريطة
        self.map_info_label = tk.Label(
            frame,
            text="لم يتم اختيار خريطة",
            bg='#f0f0f0',
            fg='#666'
        )
        self.map_info_label.pack(pady=5)
        
    def create_info_section(self, parent):
        """إنشاء قسم المعلومات"""
        frame = tk.LabelFrame(
            parent,
            text="ℹ️ معلومات ونتائج",
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#1976D2'
        )
        frame.pack(fill='both', expand=True, pady=5)
        
        # منطقة النص
        self.info_text = scrolledtext.ScrolledText(
            frame,
            height=10,
            bg='white',
            fg='black',
            font=('Consolas', 9)
        )
        self.info_text.pack(fill='both', expand=True, padx=10, pady=5)
        
        # رسالة ترحيب
        welcome_msg = """🎨 مرحباً بك في Pixel Map Converter!

هذا عرض توضيحي مبسط للتطبيق. يمكنك:

1. اختيار صورة وتحويلها إلى خريطة بكسلات (JSON)
2. اختيار خريطة بكسلات وإعادة بناء الصورة منها
3. مشاهدة المعلومات والنتائج هنا

ملاحظة: هذه نسخة مبسطة للعرض التوضيحي فقط.
للحصول على النسخة الكاملة، يرجى تثبيت Python والمكتبات المطلوبة.
"""
        self.info_text.insert('1.0', welcome_msg)
        
    def select_image(self):
        """اختيار صورة"""
        try:
            file_path = filedialog.askopenfilename(
                title="اختيار صورة",
                filetypes=[
                    ("صور", "*.png *.jpg *.jpeg *.bmp *.gif"),
                    ("PNG", "*.png"),
                    ("JPEG", "*.jpg *.jpeg"),
                    ("جميع الملفات", "*.*")
                ]
            )
            
            if file_path:
                self.current_image_path = file_path
                filename = os.path.basename(file_path)
                
                # محاولة الحصول على معلومات الصورة
                try:
                    with Image.open(file_path) as img:
                        width, height = img.size
                        mode = img.mode
                        file_size = os.path.getsize(file_path)
                        
                        info = f"✅ تم اختيار: {filename}\n"
                        info += f"الأبعاد: {width}x{height}\n"
                        info += f"النمط: {mode}\n"
                        info += f"الحجم: {self.format_file_size(file_size)}"
                        
                        self.image_info_label.config(text=info, fg='green')
                        self.convert_to_map_btn.config(state='normal')
                        
                        self.log_message(f"📷 تم اختيار الصورة: {filename}")
                        self.log_message(f"   الأبعاد: {width}x{height}")
                        self.log_message(f"   الحجم: {self.format_file_size(file_size)}")
                        
                except Exception as e:
                    self.image_info_label.config(text=f"❌ خطأ في قراءة الصورة: {str(e)}", fg='red')
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في اختيار الصورة: {str(e)}")
            
    def select_map(self):
        """اختيار خريطة بكسلات"""
        try:
            file_path = filedialog.askopenfilename(
                title="اختيار خريطة بكسلات",
                filetypes=[
                    ("JSON", "*.json"),
                    ("جميع الملفات", "*.*")
                ]
            )
            
            if file_path:
                self.current_map_path = file_path
                filename = os.path.basename(file_path)
                
                # محاولة قراءة الخريطة
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if 'pixels' in data and 'metadata' in data:
                        metadata = data['metadata']
                        pixels = data['pixels']
                        
                        info = f"✅ تم اختيار: {filename}\n"
                        info += f"الأبعاد: {metadata.get('width', '?')}x{metadata.get('height', '?')}\n"
                        info += f"البكسلات: {len(pixels):,}\n"
                        info += f"الألوان الفريدة: {metadata.get('unique_colors', '?')}"
                        
                        self.map_info_label.config(text=info, fg='green')
                        self.convert_to_image_btn.config(state='normal')
                        self.pixel_map_data = data
                        
                        self.log_message(f"🗺️ تم اختيار الخريطة: {filename}")
                        self.log_message(f"   البكسلات: {len(pixels):,}")
                        
                    else:
                        self.map_info_label.config(text="❌ تنسيق خريطة غير صحيح", fg='red')
                        
                except Exception as e:
                    self.map_info_label.config(text=f"❌ خطأ في قراءة الخريطة: {str(e)}", fg='red')
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في اختيار الخريطة: {str(e)}")
            
    def convert_image_to_map(self):
        """تحويل صورة إلى خريطة بكسلات"""
        if not self.current_image_path:
            messagebox.showwarning("تحذير", "يرجى اختيار صورة أولاً")
            return
            
        self.log_message("🔄 بدء تحويل الصورة إلى خريطة...")
        
        # تشغيل التحويل في خيط منفصل
        threading.Thread(target=self._convert_image_thread, daemon=True).start()
        
    def _convert_image_thread(self):
        """تحويل الصورة في خيط منفصل"""
        try:
            with Image.open(self.current_image_path) as img:
                # تحويل إلى RGB إذا لزم الأمر
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                width, height = img.size
                pixels = img.load()
                
                # إنشاء خريطة البكسلات
                pixel_map = {
                    "metadata": {
                        "width": width,
                        "height": height,
                        "total_pixels": width * height,
                        "original_file": os.path.basename(self.current_image_path),
                        "version": "1.0"
                    },
                    "pixels": []
                }
                
                # تحويل البكسلات
                unique_colors = set()
                for y in range(height):
                    for x in range(width):
                        r, g, b = pixels[x, y]
                        hex_color = f"#{r:02x}{g:02x}{b:02x}"
                        unique_colors.add(hex_color)
                        
                        pixel_map["pixels"].append({
                            "x": x,
                            "y": y,
                            "color": hex_color
                        })
                
                pixel_map["metadata"]["unique_colors"] = len(unique_colors)
                
                # حفظ الخريطة
                os.makedirs("output", exist_ok=True)
                base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
                output_path = f"output/{base_name}_pixel_map.json"
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(pixel_map, f, indent=2, ensure_ascii=False)
                
                # تحديث الواجهة
                self.root.after(0, lambda: self._on_conversion_complete(output_path, len(unique_colors)))
                
        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ خطأ في التحويل: {str(e)}"))
            
    def _on_conversion_complete(self, output_path, unique_colors):
        """عند اكتمال التحويل"""
        file_size = os.path.getsize(output_path)
        
        self.log_message("✅ تم تحويل الصورة بنجاح!")
        self.log_message(f"   ملف الإخراج: {output_path}")
        self.log_message(f"   الألوان الفريدة: {unique_colors:,}")
        self.log_message(f"   حجم الخريطة: {self.format_file_size(file_size)}")
        
        messagebox.showinfo("نجح", f"تم تحويل الصورة بنجاح!\nالملف محفوظ في: {output_path}")
        
    def convert_map_to_image(self):
        """تحويل خريطة إلى صورة"""
        if not self.current_map_path or not self.pixel_map_data:
            messagebox.showwarning("تحذير", "يرجى اختيار خريطة صحيحة أولاً")
            return
            
        self.log_message("🔄 بدء إعادة بناء الصورة من الخريطة...")
        
        # تشغيل التحويل في خيط منفصل
        threading.Thread(target=self._convert_map_thread, daemon=True).start()
        
    def _convert_map_thread(self):
        """تحويل الخريطة في خيط منفصل"""
        try:
            data = self.pixel_map_data
            metadata = data['metadata']
            pixels_data = data['pixels']
            
            width = metadata['width']
            height = metadata['height']
            
            # إنشاء صورة جديدة
            img = Image.new('RGB', (width, height), 'white')
            pixels = img.load()
            
            # ملء البكسلات
            for pixel in pixels_data:
                x, y = pixel['x'], pixel['y']
                hex_color = pixel['color']
                
                # تحويل hex إلى RGB
                hex_color = hex_color.lstrip('#')
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                
                pixels[x, y] = (r, g, b)
            
            # حفظ الصورة
            os.makedirs("output", exist_ok=True)
            base_name = os.path.splitext(os.path.basename(self.current_map_path))[0]
            output_path = f"output/{base_name}_restored.png"
            
            img.save(output_path)
            
            # تحديث الواجهة
            self.root.after(0, lambda: self._on_rebuild_complete(output_path))
            
        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ خطأ في إعادة البناء: {str(e)}"))
            
    def _on_rebuild_complete(self, output_path):
        """عند اكتمال إعادة البناء"""
        file_size = os.path.getsize(output_path)
        
        self.log_message("✅ تم إعادة بناء الصورة بنجاح!")
        self.log_message(f"   ملف الإخراج: {output_path}")
        self.log_message(f"   حجم الصورة: {self.format_file_size(file_size)}")
        
        messagebox.showinfo("نجح", f"تم إعادة بناء الصورة بنجاح!\nالصورة محفوظة في: {output_path}")
        
    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"
        
    def log_message(self, message):
        """إضافة رسالة إلى سجل المعلومات"""
        self.info_text.insert('end', f"\n{message}")
        self.info_text.see('end')
        self.root.update()

def main():
    """الدالة الرئيسية"""
    try:
        root = tk.Tk()
        app = SimplePixelMapApp(root)
        root.mainloop()
    except ImportError as e:
        print(f"خطأ: مكتبة مفقودة - {e}")
        print("يرجى تثبيت Python مع مكتبة tkinter")
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
