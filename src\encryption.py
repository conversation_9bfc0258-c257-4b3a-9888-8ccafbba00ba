"""
نظام التشفير والمفاتيح الخاصة
"""

import json
import base64
import hashlib
import secrets
import string
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class PixelMapEncryption:
    """
    فئة تشفير خرائط البكسلات
    """
    
    def __init__(self):
        self.key_length = 50
        self.salt_length = 32
    
    def generate_user_key(self, length: int = 50) -> str:
        """
        توليد مفتاح خاص للمستخدم
        
        Args:
            length: طول المفتاح (افتراضي 50 حرف)
        
        Returns:
            مفتاح خاص عشوائي
        """
        # أحرف وأرقام للمفتاح
        chars = string.ascii_letters + string.digits
        
        # توليد مفتاح عشوائي آمن
        key = ''.join(secrets.choice(chars) for _ in range(length))
        
        return key
    
    def _derive_encryption_key(self, user_key: str, salt: bytes) -> bytes:
        """
        اشتقاق مفتاح التشفير من مفتاح المستخدم
        
        Args:
            user_key: مفتاح المستخدم
            salt: الملح للتشفير
        
        Returns:
            مفتاح التشفير المشتق
        """
        # تحويل مفتاح المستخدم إلى bytes
        password = user_key.encode('utf-8')
        
        # إنشاء مفتاح التشفير باستخدام PBKDF2
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key
    
    def encrypt_pixel_map(self, pixel_map_data: Dict[str, Any], user_key: str) -> Dict[str, Any]:
        """
        تشفير خريطة البكسلات
        
        Args:
            pixel_map_data: بيانات خريطة البكسلات
            user_key: مفتاح المستخدم
        
        Returns:
            خريطة البكسلات المشفرة
        """
        try:
            # توليد ملح عشوائي
            salt = secrets.token_bytes(self.salt_length)
            
            # اشتقاق مفتاح التشفير
            encryption_key = self._derive_encryption_key(user_key, salt)
            
            # إنشاء كائن التشفير
            fernet = Fernet(encryption_key)
            
            # تحويل البيانات إلى JSON
            json_data = json.dumps(pixel_map_data, separators=(',', ':'), ensure_ascii=False)
            
            # تشفير البيانات
            encrypted_data = fernet.encrypt(json_data.encode('utf-8'))
            
            # إنشاء hash للمفتاح للتحقق
            key_hash = hashlib.sha256(user_key.encode('utf-8')).hexdigest()[:16]
            
            # إنشاء البيانات المشفرة النهائية
            encrypted_map = {
                "encrypted": True,
                "version": "1.0",
                "algorithm": "Fernet",
                "key_hash": key_hash,
                "salt": base64.b64encode(salt).decode('utf-8'),
                "data": base64.b64encode(encrypted_data).decode('utf-8'),
                "metadata": {
                    "original_format": pixel_map_data.get("format_version", "1.0"),
                    "encrypted_at": self._get_timestamp(),
                    "key_length": len(user_key)
                }
            }
            
            return encrypted_map
            
        except Exception as e:
            raise Exception(f"فشل في تشفير البيانات: {str(e)}")
    
    def decrypt_pixel_map(self, encrypted_data: Dict[str, Any], user_key: str) -> Dict[str, Any]:
        """
        فك تشفير خريطة البكسلات
        
        Args:
            encrypted_data: البيانات المشفرة
            user_key: مفتاح المستخدم
        
        Returns:
            خريطة البكسلات الأصلية
        """
        try:
            # التحقق من أن البيانات مشفرة
            if not encrypted_data.get("encrypted", False):
                raise Exception("البيانات غير مشفرة")
            
            # التحقق من صحة المفتاح
            key_hash = hashlib.sha256(user_key.encode('utf-8')).hexdigest()[:16]
            if key_hash != encrypted_data.get("key_hash"):
                raise Exception("مفتاح غير صحيح")
            
            # استخراج الملح
            salt = base64.b64decode(encrypted_data["salt"])
            
            # اشتقاق مفتاح التشفير
            encryption_key = self._derive_encryption_key(user_key, salt)
            
            # إنشاء كائن فك التشفير
            fernet = Fernet(encryption_key)
            
            # فك تشفير البيانات
            encrypted_bytes = base64.b64decode(encrypted_data["data"])
            decrypted_bytes = fernet.decrypt(encrypted_bytes)
            
            # تحويل إلى JSON
            decrypted_json = decrypted_bytes.decode('utf-8')
            pixel_map_data = json.loads(decrypted_json)
            
            return pixel_map_data
            
        except Exception as e:
            if "مفتاح غير صحيح" in str(e):
                raise Exception("❌ مفتاح غير صحيح - لا يمكن فك التشفير")
            else:
                raise Exception(f"فشل في فك التشفير: {str(e)}")
    
    def verify_key(self, encrypted_data: Dict[str, Any], user_key: str) -> bool:
        """
        التحقق من صحة المفتاح
        
        Args:
            encrypted_data: البيانات المشفرة
            user_key: مفتاح المستخدم
        
        Returns:
            True إذا كان المفتاح صحيح
        """
        try:
            if not encrypted_data.get("encrypted", False):
                return True  # البيانات غير مشفرة
            
            key_hash = hashlib.sha256(user_key.encode('utf-8')).hexdigest()[:16]
            return key_hash == encrypted_data.get("key_hash")
            
        except:
            return False
    
    def is_encrypted(self, data: Dict[str, Any]) -> bool:
        """
        التحقق من أن البيانات مشفرة
        
        Args:
            data: البيانات للفحص
        
        Returns:
            True إذا كانت البيانات مشفرة
        """
        return data.get("encrypted", False) == True
    
    def get_encryption_info(self, encrypted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        الحصول على معلومات التشفير
        
        Args:
            encrypted_data: البيانات المشفرة
        
        Returns:
            معلومات التشفير
        """
        if not self.is_encrypted(encrypted_data):
            return {"encrypted": False}
        
        metadata = encrypted_data.get("metadata", {})
        
        return {
            "encrypted": True,
            "algorithm": encrypted_data.get("algorithm", "غير محدد"),
            "version": encrypted_data.get("version", "غير محدد"),
            "encrypted_at": metadata.get("encrypted_at", "غير محدد"),
            "key_length": metadata.get("key_length", "غير محدد"),
            "original_format": metadata.get("original_format", "غير محدد")
        }
    
    def _get_timestamp(self) -> str:
        """الحصول على الوقت الحالي"""
        from datetime import datetime
        return datetime.now().isoformat()

class KeyManager:
    """
    مدير المفاتيح الخاصة
    """
    
    def __init__(self):
        self.encryption = PixelMapEncryption()
    
    def generate_new_key(self) -> Dict[str, str]:
        """
        توليد مفتاح جديد مع معلومات إضافية
        
        Returns:
            معلومات المفتاح الجديد
        """
        key = self.encryption.generate_user_key()
        
        return {
            "key": key,
            "length": len(key),
            "created_at": self.encryption._get_timestamp(),
            "fingerprint": hashlib.sha256(key.encode('utf-8')).hexdigest()[:8],
            "instructions": "احفظ هذا المفتاح في مكان آمن - لن تتمكن من استرجاع صورك بدونه!"
        }
    
    def validate_key_format(self, key: str) -> Dict[str, Any]:
        """
        التحقق من صحة تنسيق المفتاح
        
        Args:
            key: المفتاح للفحص
        
        Returns:
            نتيجة التحقق
        """
        if not key:
            return {"valid": False, "error": "المفتاح فارغ"}
        
        if len(key) < 20:
            return {"valid": False, "error": "المفتاح قصير جداً (يجب أن يكون 20 حرف على الأقل)"}
        
        if len(key) > 100:
            return {"valid": False, "error": "المفتاح طويل جداً (يجب أن يكون أقل من 100 حرف)"}
        
        # التحقق من الأحرف المسموحة
        allowed_chars = set(string.ascii_letters + string.digits)
        if not all(c in allowed_chars for c in key):
            return {"valid": False, "error": "المفتاح يحتوي على أحرف غير مسموحة (فقط أحرف وأرقام)"}
        
        return {
            "valid": True,
            "length": len(key),
            "fingerprint": hashlib.sha256(key.encode('utf-8')).hexdigest()[:8]
        }
