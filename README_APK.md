# 📱 Pixel Map Converter - تطبيق أندرويد

## 🎯 نظرة عامة

**Pixel Map Converter** هو تطبيق أندرويد متقدم لتحويل الصور إلى خريطة بكسلات (JSON) وإعادة بنائها مرة أخرى. التطبيق مبني باستخدام **Kivy** و **KivyMD** مع واجهة مستخدم حديثة وسهلة الاستخدام.

## ✨ المميزات

### 🎨 واجهة المستخدم
- **Material Design** حديث وجميل
- **دعم اللغة العربية** كامل
- **ألوان متناسقة** وتصميم احترافي
- **رسائل تفاعلية** واضحة

### 📱 الوظائف الأساسية
- 📷 **اختيار الصور** من معرض الجهاز
- 🔄 **تحويل الصور** إلى خريطة بكسلات JSON
- 🖼️ **إعادة بناء الصور** من خريطة البكسلات
- ℹ️ **عرض معلومات** مفصلة عن الصور والخرائط
- 💾 **حفظ تلقائي** في مجلد منظم

### 🔧 المميزات التقنية
- ⚡ **معالجة متوازية** للعمليات الطويلة
- 🗂️ **إدارة ملفات** ذكية وتلقائية
- 🎨 **دعم الشفافية** في الصور
- 📦 **ضغط ذكي** للبيانات
- 📱 **واجهة مستجيبة** لا تتجمد

## 🚀 البدء السريع

### 📥 تحميل APK

#### الطريقة 1: بناء APK بنفسك
```bash
# Windows
build_apk.bat

# Linux/macOS
chmod +x build_apk.sh
./build_apk.sh

# Python (عام)
python build_apk.py
```

#### الطريقة 2: تحميل APK جاهز
*(سيتم توفير رابط التحميل لاحقاً)*

### 📱 التثبيت
1. **تفعيل "مصادر غير معروفة"** في إعدادات الأندرويد
2. **تحميل ملف APK** على الجهاز
3. **النقر على الملف** لبدء التثبيت
4. **منح الصلاحيات** المطلوبة

### 🔐 الصلاحيات المطلوبة
- **قراءة التخزين** - لاختيار الصور من المعرض
- **كتابة التخزين** - لحفظ الملفات المُنشأة

## 📖 طريقة الاستخدام

### 1. تحويل صورة إلى خريطة بكسلات

1. **افتح التطبيق**
2. **اضغط "اختيار صورة"** في قسم تحويل الصور
3. **اختر صورة** من معرض الجهاز
4. **اعرض معلومات الصورة** (الأبعاد، الحجم)
5. **اضغط "تحويل إلى خريطة"**
6. **انتظر اكتمال التحويل**
7. **ستجد الخريطة** في مجلد `PixelMapConverter/output/`

### 2. إعادة بناء صورة من خريطة

1. **اضغط "اختيار خريطة"** في قسم إعادة البناء
2. **اختر ملف JSON** من مجلد الإخراج
3. **اعرض معلومات الخريطة**
4. **اضغط "إعادة بناء الصورة"**
5. **انتظر اكتمال العملية**
6. **ستجد الصورة المُعادة** في نفس المجلد

## 📁 تنظيم الملفات

### مجلد التطبيق الرئيسي
```
/storage/emulated/0/PixelMapConverter/
├── output/                    # ملفات الإخراج
│   ├── image1_pixel_map.json  # خرائط البكسلات
│   ├── image1_restored.png    # الصور المُعادة
│   └── ...
└── examples/                  # صور تجريبية (اختياري)
    └── ...
```

### أنواع الملفات
- **`.json`** - خرائط البكسلات
- **`_restored.png`** - الصور المُعادة البناء
- **`_pixel_map.json`** - خرائط مُحولة من صور

## 🎯 أمثلة عملية

### مثال 1: تحويل صورة شخصية
1. اختر صورة شخصية من المعرض
2. حولها إلى خريطة بكسلات
3. شاهد كيف يتم تمثيل الصورة كبيانات JSON
4. أعد بناء الصورة من الخريطة

### مثال 2: ضغط صورة بسيطة
1. اختر صورة بألوان قليلة (مثل لوجو)
2. حولها إلى خريطة
3. قارن حجم الملف الأصلي مع الخريطة
4. لاحظ نسبة الضغط المحققة

## 🔧 المواصفات التقنية

### متطلبات النظام
- **Android 5.0+** (API 21)
- **50 MB** مساحة تخزين فارغة
- **RAM 2GB+** (مُفضل)

### الصيغ المدعومة
- **الصور**: PNG, JPG, JPEG, BMP
- **الخرائط**: JSON

### حدود الأداء
- **الحد الأقصى للصورة**: 2048x2048 بكسل
- **الذاكرة المستخدمة**: تعتمد على حجم الصورة
- **وقت التحويل**: يعتمد على تعقيد الصورة

## 🎨 لقطات شاشة

### الشاشة الرئيسية
```
┌─────────────────────────────────────┐
│        Pixel Map Converter          │
├─────────────────────────────────────┤
│                                     │
│  🎨 مرحباً بك في محول خريطة البكسلات │
│     حول صورك إلى خريطة بكسلات       │
│                                     │
├─────────────────────────────────────┤
│  📷 تحويل صورة إلى خريطة بكسلات     │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │ اختيار صورة │ │ تحويل إلى خريطة │ │
│  └─────────────┘ └─────────────────┘ │
│  لم يتم اختيار صورة                 │
├─────────────────────────────────────┤
│  🗺️ تحويل خريطة إلى صورة            │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │ اختيار خريطة│ │ إعادة بناء الصورة│ │
│  └─────────────┘ └─────────────────┘ │
│  لم يتم اختيار خريطة                │
└─────────────────────────────────────┘
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. التطبيق لا يفتح
- **تأكد من إصدار الأندرويد** (5.0+)
- **أعد تثبيت التطبيق**
- **تحقق من مساحة التخزين**

#### 2. لا يمكن اختيار الصور
- **امنح صلاحية الوصول للتخزين**
- **تأكد من وجود صور في المعرض**
- **جرب إعادة تشغيل التطبيق**

#### 3. فشل في التحويل
- **تحقق من حجم الصورة** (لا تتجاوز 2048x2048)
- **تأكد من صحة تنسيق الصورة**
- **تحقق من مساحة التخزين المتاحة**

#### 4. الملفات لا تُحفظ
- **امنح صلاحية الكتابة**
- **تحقق من مساحة التخزين**
- **تأكد من عدم حماية المجلد**

## 🚀 التطوير والمساهمة

### بناء التطبيق من المصدر
```bash
# استنساخ المشروع
git clone [repository-url]
cd pixel_map_converter

# تثبيت المتطلبات
pip install -r requirements.txt

# بناء APK
python build_apk.py
```

### المساهمة في التطوير
1. **Fork المشروع**
2. **إنشاء فرع جديد** للميزة
3. **إضافة التحسينات**
4. **اختبار التغييرات**
5. **إرسال Pull Request**

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الحر.

## 👨‍💻 المطور

تم تطوير هذا التطبيق بواسطة **Augment Agent** - مساعد ذكي للبرمجة.

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
- راجع **APK_BUILD_GUIDE.md** لدليل البناء
- راجع **APK_SUMMARY.md** للملخص التقني
- راجع **README.md** للدليل العام

### الإبلاغ عن المشاكل:
- وصف المشكلة بالتفصيل
- ذكر إصدار الأندرويد
- إرفاق لقطات شاشة إن أمكن

---

**استمتع بتحويل صورك إلى خرائط بكسلات على الأندرويد! 📱🎨✨**
