# 🐍 دليل تثبيت Python لتشغيل التطبيق

## 🎯 نظرة عامة

لتشغيل تطبيق **Pixel Map Converter**، تحتاج إلى تثبيت Python أولاً. إليك دليل شامل للتثبيت على Windows.

## 📥 تثبيت Python على Windows

### الطريقة 1: التحميل من الموقع الرسمي (الأسهل)

#### 1. تحميل Python:
- اذهب إلى: https://www.python.org/downloads/
- اضغط على **"Download Python 3.x.x"** (أحدث إصدار)
- احفظ الملف على سطح المكتب

#### 2. تثبيت Python:
1. **شغل الملف المُحمل** (python-3.x.x-amd64.exe)
2. **✅ تأكد من تحديد "Add Python to PATH"** (مهم جداً!)
3. اختر **"Install Now"**
4. انتظر اكتمال التثبيت
5. اضغط **"Close"**

#### 3. التحقق من التثبيت:
```cmd
# افتح Command Prompt واكتب:
python --version

# أو
py --version
```

### الطريقة 2: Microsoft Store (Windows 10/11)

1. افتح **Microsoft Store**
2. ابحث عن **"Python 3.x"**
3. اضغط **"Get"** أو **"Install"**
4. انتظر اكتمال التثبيت

## 🚀 تشغيل التطبيق بعد تثبيت Python

### الطريقة 1: العرض التوضيحي المبسط
```cmd
# افتح Command Prompt في مجلد المشروع
cd c:\Users\<USER>\Desktop\python

# شغل العرض التوضيحي
python demo_app_simple.py
```

### الطريقة 2: التطبيق الكامل
```cmd
# تثبيت المكتبات المطلوبة
python -m pip install -r requirements.txt

# تشغيل التطبيق الكامل
python main.py --help

# أو الواجهة التفاعلية
run_app.bat
```

### الطريقة 3: تطبيق الأندرويد (Kivy)
```cmd
# تثبيت مكتبات Kivy
python -m pip install kivy kivymd

# تشغيل تطبيق الأندرويد على سطح المكتب
python android_app.py
```

## 🔧 حل المشاكل الشائعة

### مشكلة: "python is not recognized"

#### الحل 1: إعادة تثبيت Python
- أعد تثبيت Python مع تحديد **"Add Python to PATH"**

#### الحل 2: إضافة Python يدوياً إلى PATH
1. ابحث عن **"Environment Variables"** في Windows
2. اضغط **"Edit the system environment variables"**
3. اضغط **"Environment Variables"**
4. في **"System Variables"**، ابحث عن **"Path"**
5. اضغط **"Edit"** ثم **"New"**
6. أضف مسار Python (عادة: `C:\Users\<USER>\AppData\Local\Programs\Python\Python3x\`)
7. أضف أيضاً: `C:\Users\<USER>\AppData\Local\Programs\Python\Python3x\Scripts\`
8. اضغط **"OK"** لحفظ التغييرات
9. أعد تشغيل Command Prompt

#### الحل 3: استخدام py بدلاً من python
```cmd
# جرب هذا بدلاً من python
py demo_app_simple.py
py -m pip install -r requirements.txt
```

### مشكلة: "No module named 'PIL'"
```cmd
# تثبيت Pillow
python -m pip install Pillow
```

### مشكلة: "No module named 'tkinter'"
```cmd
# tkinter مدمج مع Python، لكن إذا لم يعمل:
# أعد تثبيت Python مع تحديد "tcl/tk and IDLE"
```

## 📋 خطوات سريعة للتشغيل

### إذا كان Python مثبت:
```cmd
# 1. افتح Command Prompt
# اضغط Win+R، اكتب cmd، اضغط Enter

# 2. انتقل لمجلد المشروع
cd c:\Users\<USER>\Desktop\python

# 3. شغل العرض التوضيحي
python demo_app_simple.py
```

### إذا لم يكن Python مثبت:
1. **حمل Python** من: https://www.python.org/downloads/
2. **ثبت Python** مع تحديد "Add Python to PATH"
3. **أعد تشغيل Command Prompt**
4. **شغل التطبيق** كما هو موضح أعلاه

## 🎨 ما ستراه عند تشغيل التطبيق

### العرض التوضيحي المبسط:
- **واجهة رسومية** بسيطة وواضحة
- **أزرار لاختيار الصور** والخرائط
- **منطقة معلومات** تعرض النتائج
- **وظائف كاملة** للتحويل

### التطبيق الكامل:
- **واجهة سطر أوامر** متقدمة
- **خيارات متعددة** للتحويل
- **إحصائيات مفصلة** ومقارنات
- **دعم صيغ متعددة**

## 🆘 إذا واجهت مشاكل

### تحقق من هذه النقاط:
1. **Python مثبت؟** - `python --version`
2. **PATH مُعرف؟** - أعد تثبيت Python
3. **المكتبات مثبتة؟** - `pip install -r requirements.txt`
4. **الملفات موجودة؟** - تأكد من وجود ملفات المشروع

### احصل على المساعدة:
- راجع **README.md** للدليل الشامل
- راجع **QUICK_START.md** للبدء السريع
- راجع **INSTALL.md** للتثبيت المفصل

## 🎉 بعد التثبيت الناجح

ستتمكن من:
- ✅ **تشغيل العرض التوضيحي** المبسط
- ✅ **استخدام التطبيق الكامل** مع جميع المميزات
- ✅ **بناء APK للأندرويد** (متقدم)
- ✅ **تطوير وتخصيص** التطبيق

---

**استمتع بتجربة Pixel Map Converter! 🎨✨**
