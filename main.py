#!/usr/bin/env python3
"""
تطبيق تحويل الصور إلى خريطة بكسلات
Pixel Map Converter - الملف الرئيسي
"""

import os
import sys
import click
from colorama import init, Fore, Style

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.image_to_map import ImageToMapConverter
from src.map_to_image import MapToImageConverter
from src.utils import (
    validate_image_file, get_file_size, format_file_size,
    get_image_info, print_comparison_stats, create_output_directory
)

# تهيئة colorama للألوان في Windows
init()

def print_banner():
    """طباعة شعار التطبيق"""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    🎨 Pixel Map Converter 🎨                 ║
║                  تطبيق تحويل الصور إلى خريطة بكسلات          ║
║                        Augment Agent                         ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(banner)

@click.group()
def cli():
    """تطبيق تحويل الصور إلى خريطة بكسلات وإعادة بنائها"""
    print_banner()

@cli.command()
@click.argument('image_path', type=click.Path(exists=True))
@click.option('--output', '-o', default=None, help='مسار ملف الإخراج')
@click.option('--compress', '-c', is_flag=True, help='تفعيل الضغط (قد يؤثر على الجودة)')
@click.option('--compression-level', '-l', default='lossless',
              type=click.Choice(['low', 'medium', 'high', 'ultra', 'lossless']),
              help='مستوى الضغط (lossless=جودة 100%, high=جودة عالية, medium=متوسط, low=سريع)')
@click.option('--skip-transparent', '-t', is_flag=True, default=True,
              help='تجاهل البكسلات الشفافة')
@click.option('--encrypt', '-e', is_flag=True, help='تشفير الخريطة بمفتاح خاص')
@click.option('--key', '-k', help='مفتاح التشفير (50 حرف)')
def image_to_map(image_path, output, compress, compression_level, skip_transparent, encrypt, key):
    """تحويل صورة إلى خريطة بكسلات"""

    print(f"{Fore.YELLOW}🔄 بدء تحويل الصورة إلى خريطة بكسلات...{Style.RESET_ALL}")

    # التحقق من صحة الصورة
    if not validate_image_file(image_path):
        print(f"{Fore.RED}❌ ملف الصورة غير صحيح أو غير موجود: {image_path}{Style.RESET_ALL}")
        return

    # تحديد مسار الإخراج
    if not output:
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output = f"output/{base_name}_pixel_map.json"

    # إنشاء مجلد الإخراج
    create_output_directory("output")

    # عرض معلومات الصورة
    img_info = get_image_info(image_path)
    print(f"\n📋 معلومات الصورة:")
    print(f"   الأبعاد: {img_info.get('width')}x{img_info.get('height')}")
    print(f"   التنسيق: {img_info.get('format')}")
    print(f"   النمط: {img_info.get('mode')}")
    print(f"   حجم الملف: {format_file_size(img_info.get('file_size', 0))}")
    print(f"   عدد البكسلات: {img_info.get('size', 0):,}")

    # تحويل الصورة
    converter = ImageToMapConverter()

    # عرض معلومات الجودة والضغط
    if compress:
        if compression_level == 'lossless':
            print(f"💎 مستوى الجودة: 100% (بدون فقدان)")
        else:
            print(f"🗜️  مستوى الضغط: {compression_level}")
            print(f"⚠️  تحذير: قد يؤثر الضغط على الجودة!")
    else:
        print(f"💎 الوضع: جودة 100% (بدون ضغط)")

    # التحقق من التشفير
    encryption_key = None
    if encrypt:
        if key:
            encryption_key = key
            print(f"🔐 سيتم تشفير الخريطة بالمفتاح المحدد")
        else:
            # توليد مفتاح جديد
            from src.encryption import KeyManager
            key_manager = KeyManager()
            key_info = key_manager.generate_new_key()
            encryption_key = key_info["key"]

            print(f"🔑 تم توليد مفتاح جديد:")
            print(f"   المفتاح: {encryption_key}")
            print(f"   البصمة: {key_info['fingerprint']}")
            print(f"   ⚠️  احفظ هذا المفتاح - لن تتمكن من فك التشفير بدونه!")

    success = converter.convert_image_to_map(
        image_path=image_path,
        output_path=output,
        compress_colors=compress,
        skip_transparent=skip_transparent,
        compression_level=compression_level,
        encryption_key=encryption_key
    )

    if success:
        print(f"\n{Fore.GREEN}✅ تم التحويل بنجاح!{Style.RESET_ALL}")
        print_comparison_stats(image_path, output)
    else:
        print(f"{Fore.RED}❌ فشل في تحويل الصورة{Style.RESET_ALL}")

@cli.command()
@click.argument('map_path', type=click.Path(exists=True))
@click.option('--output', '-o', default=None, help='مسار الصورة الناتجة')
@click.option('--format', '-f', default='png',
              type=click.Choice(['png', 'jpg', 'jpeg']), help='تنسيق الصورة الناتجة')
@click.option('--background', '-b', default='white',
              type=click.Choice(['white', 'black', 'transparent']),
              help='لون الخلفية')
@click.option('--key', '-k', help='مفتاح فك التشفير (إذا كان الملف مشفر)')
def map_to_image(map_path, output, format, background, key):
    """تحويل خريطة بكسلات إلى صورة"""

    print(f"{Fore.YELLOW}🔄 بدء تحويل خريطة البكسلات إلى صورة...{Style.RESET_ALL}")

    # تحديد مسار الإخراج
    if not output:
        base_name = os.path.splitext(os.path.basename(map_path))[0]
        output = f"output/{base_name}_restored.{format}"

    # إنشاء مجلد الإخراج
    create_output_directory("output")

    # تحديد لون الخلفية
    bg_colors = {
        'white': (255, 255, 255, 255),
        'black': (0, 0, 0, 255),
        'transparent': (255, 255, 255, 0)
    }
    background_color = bg_colors.get(background, (255, 255, 255, 255))

    # تحويل الخريطة
    converter = MapToImageConverter()

    # التحقق من صحة الملف
    if not converter.validate_map_file(map_path):
        print(f"{Fore.RED}❌ ملف الخريطة غير صحيح: {map_path}{Style.RESET_ALL}")
        return

    # عرض معلومات الخريطة
    map_info = converter.get_map_info(map_path)
    print(f"\n📋 معلومات خريطة البكسلات:")
    print(f"   الأبعاد: {map_info.get('dimensions')}")
    print(f"   عدد البكسلات: {map_info.get('pixel_count', 0):,}")
    print(f"   الألوان الفريدة: {map_info.get('unique_colors')}")

    # التحقق من التشفير
    if key:
        print(f"🔐 سيتم استخدام المفتاح لفك التشفير")

    success = converter.convert_map_to_image(
        map_path=map_path,
        output_path=output,
        background_color=background_color,
        encryption_key=key
    )

    if success:
        print(f"\n{Fore.GREEN}✅ تم إنشاء الصورة بنجاح!{Style.RESET_ALL}")
        print(f"   ملف الإخراج: {output}")
    else:
        print(f"{Fore.RED}❌ فشل في إنشاء الصورة{Style.RESET_ALL}")

@cli.command()
@click.argument('file_path', type=click.Path(exists=True))
def info(file_path):
    """عرض معلومات ملف (صورة أو خريطة بكسلات)"""

    file_ext = os.path.splitext(file_path)[1].lower()

    if file_ext == '.json':
        # ملف خريطة بكسلات
        converter = MapToImageConverter()
        if converter.validate_map_file(file_path):
            info_data = converter.get_map_info(file_path)
            print(f"\n📋 معلومات خريطة البكسلات:")
            print(f"   الملف: {file_path}")
            print(f"   الأبعاد: {info_data.get('dimensions')}")
            print(f"   عدد البكسلات: {info_data.get('pixel_count', 0):,}")
            print(f"   الألوان الفريدة: {info_data.get('unique_colors')}")
            print(f"   حجم الملف: {format_file_size(get_file_size(file_path))}")
        else:
            print(f"{Fore.RED}❌ ملف خريطة البكسلات غير صحيح{Style.RESET_ALL}")

    elif file_ext in ['.png', '.jpg', '.jpeg', '.bmp', '.gif']:
        # ملف صورة
        if validate_image_file(file_path):
            info_data = get_image_info(file_path)
            print(f"\n📋 معلومات الصورة:")
            print(f"   الملف: {file_path}")
            print(f"   الأبعاد: {info_data.get('width')}x{info_data.get('height')}")
            print(f"   التنسيق: {info_data.get('format')}")
            print(f"   النمط: {info_data.get('mode')}")
            print(f"   عدد البكسلات: {info_data.get('size', 0):,}")
            print(f"   حجم الملف: {format_file_size(info_data.get('file_size', 0))}")
        else:
            print(f"{Fore.RED}❌ ملف الصورة غير صحيح{Style.RESET_ALL}")

    else:
        print(f"{Fore.RED}❌ نوع ملف غير مدعوم: {file_ext}{Style.RESET_ALL}")

if __name__ == '__main__':
    cli()
