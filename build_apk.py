#!/usr/bin/env python3
"""
سكريبت بناء APK لتطبيق Pixel Map Converter
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من Python
    try:
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
            print("❌ يتطلب Python 3.7 أو أحدث")
            return False
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    except Exception as e:
        print(f"❌ خطأ في التحقق من Python: {e}")
        return False
    
    # التحقق من buildozer
    try:
        result = subprocess.run(['buildozer', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Buildozer متوفر")
        else:
            print("❌ Buildozer غير مثبت")
            print("قم بتثبيته باستخدام: pip install buildozer")
            return False
    except FileNotFoundError:
        print("❌ Buildozer غير مثبت")
        print("قم بتثبيته باستخدام: pip install buildozer")
        return False
    
    return True

def setup_android_environment():
    """إعداد بيئة الأندرويد"""
    print("\n🔧 إعداد بيئة الأندرويد...")
    
    # التحقق من متغيرات البيئة المطلوبة
    required_vars = ['ANDROID_HOME', 'ANDROID_SDK_ROOT']
    missing_vars = []
    
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        print("يرجى تثبيت Android SDK وتعيين متغيرات البيئة")
        return False
    
    print("✅ بيئة الأندرويد جاهزة")
    return True

def prepare_build_files():
    """تحضير ملفات البناء"""
    print("\n📁 تحضير ملفات البناء...")
    
    # نسخ الملف الرئيسي
    if os.path.exists('android_app.py'):
        shutil.copy2('android_app.py', 'main.py')
        print("✅ تم نسخ android_app.py إلى main.py")
    else:
        print("❌ ملف android_app.py غير موجود")
        return False
    
    # التأكد من وجود مجلد src
    if not os.path.exists('src'):
        print("❌ مجلد src غير موجود")
        return False
    
    print("✅ ملفات البناء جاهزة")
    return True

def build_debug_apk():
    """بناء APK للاختبار"""
    print("\n🔨 بناء APK للاختبار...")
    
    try:
        # تشغيل buildozer
        result = subprocess.run(
            ['buildozer', 'android', 'debug'],
            cwd=os.getcwd(),
            capture_output=False,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ تم بناء APK بنجاح!")
            
            # البحث عن ملف APK
            bin_dir = Path('./bin')
            if bin_dir.exists():
                apk_files = list(bin_dir.glob('*.apk'))
                if apk_files:
                    apk_file = apk_files[0]
                    print(f"📱 ملف APK: {apk_file}")
                    print(f"📏 حجم الملف: {apk_file.stat().st_size / (1024*1024):.2f} MB")
                    return True
            
            print("⚠️  تم البناء ولكن لم يتم العثور على ملف APK")
            return False
        else:
            print("❌ فشل في بناء APK")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في البناء: {e}")
        return False

def build_release_apk():
    """بناء APK للإصدار النهائي"""
    print("\n🚀 بناء APK للإصدار النهائي...")
    
    try:
        result = subprocess.run(
            ['buildozer', 'android', 'release'],
            cwd=os.getcwd(),
            capture_output=False,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ تم بناء APK النهائي بنجاح!")
            return True
        else:
            print("❌ فشل في بناء APK النهائي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في البناء: {e}")
        return False

def clean_build():
    """تنظيف ملفات البناء"""
    print("\n🧹 تنظيف ملفات البناء...")
    
    dirs_to_clean = ['.buildozer', '__pycache__']
    files_to_clean = ['main.py']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🗑️  تم حذف مجلد: {dir_name}")
    
    for file_name in files_to_clean:
        if os.path.exists(file_name) and file_name != 'main_android.py':
            os.remove(file_name)
            print(f"🗑️  تم حذف ملف: {file_name}")
    
    print("✅ تم التنظيف")

def main():
    """الدالة الرئيسية"""
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                    📱 بناء APK للأندرويد 📱                  ║")
    print("║                  Pixel Map Converter                         ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    
    # قائمة الخيارات
    print("\nاختر نوع البناء:")
    print("1. بناء APK للاختبار (Debug)")
    print("2. بناء APK للإصدار النهائي (Release)")
    print("3. تنظيف ملفات البناء")
    print("4. خروج")
    
    choice = input("\nأدخل اختيارك (1-4): ").strip()
    
    if choice == '1':
        # بناء APK للاختبار
        if not check_requirements():
            return
        
        if not prepare_build_files():
            return
        
        build_debug_apk()
        
    elif choice == '2':
        # بناء APK للإصدار النهائي
        if not check_requirements():
            return
        
        if not setup_android_environment():
            return
        
        if not prepare_build_files():
            return
        
        build_release_apk()
        
    elif choice == '3':
        # تنظيف
        clean_build()
        
    elif choice == '4':
        print("👋 وداعاً!")
        return
        
    else:
        print("❌ اختيار غير صحيح")

if __name__ == '__main__':
    main()
