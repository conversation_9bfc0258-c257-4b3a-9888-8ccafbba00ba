// Firebase Configuration
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";
import { getStorage, ref, uploadBytes, getDownloadURL, deleteObject } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js";
import { getFirestore, collection, addDoc, getDocs, doc, deleteDoc, query, where, orderBy, limit } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBdYEr2epZPoYc3DtG-aWFQlCZv8yuIzQ4",
  authDomain: "mage-41f51.firebaseapp.com",
  projectId: "mage-41f51",
  storageBucket: "mage-41f51.firebasestorage.app",
  messagingSenderId: "949188030661",
  appId: "1:949188030661:web:d426ce91c9763e8cea40ac",
  measurementId: "G-J12L13XPHZ"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);
const storage = getStorage(app);
const db = getFirestore(app);

// Firebase Services
class FirebaseService {
    constructor() {
        this.storage = storage;
        this.db = db;
        this.analytics = analytics;
    }

    // رفع ملف مشفر إلى Firebase Storage
    async uploadEncryptedFile(file, fileName, userFingerprint) {
        try {
            const storageRef = ref(this.storage, `encrypted-maps/${userFingerprint}/${fileName}`);
            const snapshot = await uploadBytes(storageRef, file);
            const downloadURL = await getDownloadURL(snapshot.ref);
            
            // حفظ معلومات الملف في Firestore
            await addDoc(collection(this.db, 'encrypted-files'), {
                fileName: fileName,
                downloadURL: downloadURL,
                userFingerprint: userFingerprint,
                uploadedAt: new Date(),
                fileSize: file.size,
                fileType: 'encrypted-pixel-map'
            });

            return {
                success: true,
                downloadURL: downloadURL,
                message: 'تم رفع الملف المشفر بنجاح!'
            };
        } catch (error) {
            console.error('خطأ في رفع الملف:', error);
            return {
                success: false,
                error: error.message,
                message: 'فشل في رفع الملف'
            };
        }
    }

    // الحصول على ملفات المستخدم
    async getUserFiles(userFingerprint) {
        try {
            const q = query(
                collection(this.db, 'encrypted-files'),
                where('userFingerprint', '==', userFingerprint),
                orderBy('uploadedAt', 'desc'),
                limit(50)
            );
            
            const querySnapshot = await getDocs(q);
            const files = [];
            
            querySnapshot.forEach((doc) => {
                files.push({
                    id: doc.id,
                    ...doc.data()
                });
            });

            return {
                success: true,
                files: files
            };
        } catch (error) {
            console.error('خطأ في جلب الملفات:', error);
            return {
                success: false,
                error: error.message,
                files: []
            };
        }
    }

    // حذف ملف
    async deleteFile(fileId, filePath, userFingerprint) {
        try {
            // حذف من Storage
            const storageRef = ref(this.storage, filePath);
            await deleteObject(storageRef);
            
            // حذف من Firestore
            await deleteDoc(doc(this.db, 'encrypted-files', fileId));

            return {
                success: true,
                message: 'تم حذف الملف بنجاح!'
            };
        } catch (error) {
            console.error('خطأ في حذف الملف:', error);
            return {
                success: false,
                error: error.message,
                message: 'فشل في حذف الملف'
            };
        }
    }

    // تحميل ملف مشفر
    async downloadEncryptedFile(downloadURL) {
        try {
            const response = await fetch(downloadURL);
            if (!response.ok) {
                throw new Error('فشل في تحميل الملف');
            }
            
            const blob = await response.blob();
            const text = await blob.text();
            
            return {
                success: true,
                data: JSON.parse(text)
            };
        } catch (error) {
            console.error('خطأ في تحميل الملف:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // إحصائيات الاستخدام
    async logUsage(action, details = {}) {
        try {
            await addDoc(collection(this.db, 'usage-stats'), {
                action: action,
                details: details,
                timestamp: new Date(),
                userAgent: navigator.userAgent,
                ip: await this.getUserIP()
            });
        } catch (error) {
            console.error('خطأ في تسجيل الإحصائيات:', error);
        }
    }

    // الحصول على IP المستخدم
    async getUserIP() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            return 'unknown';
        }
    }

    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // تنسيق التاريخ
    formatDate(date) {
        if (date && date.toDate) {
            return date.toDate().toLocaleString('ar-SA');
        }
        return new Date(date).toLocaleString('ar-SA');
    }
}

// تصدير الخدمة
window.firebaseService = new FirebaseService();

// تسجيل زيارة الموقع
window.addEventListener('load', () => {
    window.firebaseService.logUsage('page-visit', {
        page: window.location.pathname,
        referrer: document.referrer
    });
});

export { FirebaseService };
