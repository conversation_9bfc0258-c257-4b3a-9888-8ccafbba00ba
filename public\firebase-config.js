// Firebase Configuration for Image Encryption
// إعدادات Firebase للمشروع الجديد

// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCCyDbFigvyA53SEPXBNkNJpRNr-0Vp7Qo",
  authDomain: "toika-369.firebaseapp.com",
  databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
  projectId: "toika-369",
  storageBucket: "toika-369.appspot.com",
  messagingSenderId: "300804286264",
  appId: "1:300804286264:web:ebfdb7bea9ecd6135403cd",
  measurementId: "G-BCTQYSJ716"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);

console.log('🔥 Firebase initialized successfully for Image Encryption');
console.log('🔐 Project: toika-369');
