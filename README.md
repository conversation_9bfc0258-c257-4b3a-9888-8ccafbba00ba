# 🎨 Pixel Map Converter - محول خريطة البكسلات

تطبيق Python متقدم لتحويل الصور إلى خريطة بكسلات (Pixel Map) وإعادة بنائها مرة أخرى. يتيح لك هذا التطبيق ضغط الصور بطريقة فريدة وتخزينها كبيانات JSON قابلة للقراءة.

## ✨ المميزات

- 🔄 **تحويل ثنائي الاتجاه**: صورة ← خريطة بكسلات ← صورة
- 📦 **ضغط ذكي**: تقليل حجم البيانات مع الحفاظ على الجودة
- 🎨 **دعم الشفافية**: التعامل مع قنوات الألفا
- 📊 **إحصائيات مفصلة**: مقارنة الأحجام ونسب الضغط
- 🖼️ **صيغ متعددة**: PNG, JPG, JPEG, BMP
- 🎯 **واجهة سهلة**: أوامر بسيطة عبر سطر الأوامر
- 🌈 **ألوان تفاعلية**: واجهة ملونة وواضحة

## 🚀 التثبيت السريع

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python main.py --help
```

## 📖 طريقة الاستخدام

### تحويل صورة إلى خريطة بكسلات
```bash
# تحويل بسيط
python main.py image-to-map image.png

# تحويل مع خيارات متقدمة
python main.py image-to-map image.png --output my_map.json --compress --skip-transparent
```

### تحويل خريطة بكسلات إلى صورة
```bash
# إعادة بناء بسيطة
python main.py map-to-image pixel_map.json

# إعادة بناء مع خيارات
python main.py map-to-image pixel_map.json --output restored.png --format png --background white
```

### عرض معلومات الملف
```bash
# معلومات صورة
python main.py info image.png

# معلومات خريطة بكسلات
python main.py info pixel_map.json
```

## 🎯 أمثلة عملية

### مثال 1: تحويل صورة صغيرة
```bash
# تحويل صورة إلى خريطة
python main.py image-to-map examples/small_image.png

# إعادة بناء الصورة
python main.py map-to-image output/small_image_pixel_map.json
```

### مثال 2: ضغط متقدم
```bash
# تحويل مع ضغط وتجاهل الشفافية
python main.py image-to-map logo.png --compress --skip-transparent --output compressed_map.json
```

### مثال 3: تنسيقات مختلفة
```bash
# إنشاء صورة JPEG مع خلفية سوداء
python main.py map-to-image pixel_map.json --format jpg --background black --output result.jpg
```

## 📁 هيكل المشروع

```
pixel_map_converter/
├── src/                    # الكود المصدري
│   ├── __init__.py
│   ├── image_to_map.py     # تحويل صورة → خريطة
│   ├── map_to_image.py     # تحويل خريطة → صورة
│   ├── compression.py      # خوارزميات الضغط
│   └── utils.py           # وظائف مساعدة
├── output/                # ملفات الإخراج
├── examples/              # صور للاختبار
├── main.py               # الملف الرئيسي
├── requirements.txt      # المتطلبات
└── README.md            # هذا الملف
```

## 🔧 خيارات التحويل

### تحويل صورة إلى خريطة
- `--output, -o`: مسار ملف الإخراج
- `--compress, -c`: تفعيل ضغط الألوان
- `--skip-transparent, -t`: تجاهل البكسلات الشفافة

### تحويل خريطة إلى صورة
- `--output, -o`: مسار الصورة الناتجة
- `--format, -f`: تنسيق الصورة (png, jpg, jpeg)
- `--background, -b`: لون الخلفية (white, black, transparent)

## 📊 تنسيق خريطة البكسلات

```json
{
  "metadata": {
    "width": 100,
    "height": 100,
    "total_pixels": 10000,
    "unique_colors": 256,
    "version": "1.0"
  },
  "pixels": [
    {"x": 0, "y": 0, "color": "#ffffff"},
    {"x": 1, "y": 0, "color": "#ff0000"},
    {"x": 2, "y": 0, "color": "#00ff00", "alpha": 128}
  ]
}
```

## 🎨 أمثلة الاستخدام المتقدم

### معالجة دفعية
```bash
# معالجة عدة صور
for img in *.png; do
    python main.py image-to-map "$img"
done
```

### مقارنة الأحجام
```bash
# عرض إحصائيات مفصلة
python main.py info original.png
python main.py info pixel_map.json
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في تثبيت Pillow**
   ```bash
   pip install --upgrade Pillow
   ```

2. **ملف الصورة تالف**
   ```bash
   python main.py info image.png  # للتحقق من صحة الملف
   ```

3. **نفاد الذاكرة مع الصور الكبيرة**
   - استخدم صور أصغر أو قم بتقسيم الصورة

## 🚀 تطوير مستقبلي

- [ ] واجهة رسومية (GUI)
- [ ] دعم الفيديو
- [ ] خوارزميات ضغط متقدمة
- [ ] دعم صيغ إضافية
- [ ] معالجة متوازية
- [ ] API ويب

## 📝 المتطلبات

- Python 3.7+
- Pillow (PIL)
- Click
- Colorama
- tqdm
- numpy

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الحر.

## 👨‍💻 المطور

تم تطوير هذا التطبيق بواسطة **Augment Agent** - مساعد ذكي للبرمجة.

---

**استمتع بتحويل صورك إلى خرائط بكسلات! 🎨✨**
