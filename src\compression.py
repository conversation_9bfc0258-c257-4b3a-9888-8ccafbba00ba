"""
وظائف ضغط وتحسين خريطة البكسلات
"""

from typing import List, Dict, Any, Tuple
import json
from collections import defaultdict


class PixelMapCompressor:
    """
    فئة ضغط خريطة البكسلات
    """
    
    def __init__(self):
        self.compression_stats = {}
    
    def compress_by_color_grouping(self, pixel_map: List[Dict]) -> List[Dict]:
        """
        ضغط عبر تجميع الألوان المتشابهة
        """
        color_groups = defaultdict(list)
        
        # تجميع البكسلات حسب اللون
        for pixel in pixel_map:
            color = pixel["color"]
            color_groups[color].append((pixel["x"], pixel["y"]))
        
        # إنشاء خريطة مضغوطة
        compressed_map = []
        for color, positions in color_groups.items():
            if len(positions) == 1:
                # بكسل واحد
                x, y = positions[0]
                compressed_map.append({
                    "x": x,
                    "y": y,
                    "color": color
                })
            else:
                # عدة بكسلات بنفس اللون
                compressed_map.append({
                    "color": color,
                    "positions": positions
                })
        
        return compressed_map
    
    def compress_by_run_length_encoding(self, pixel_map: List[Dict]) -> List[Dict]:
        """
        ضغط باستخدام Run-Length Encoding
        """
        if not pixel_map:
            return []
        
        # ترتيب البكسلات حسب الموقع
        sorted_pixels = sorted(pixel_map, key=lambda p: (p["y"], p["x"]))
        
        compressed = []
        current_color = sorted_pixels[0]["color"]
        current_start = (sorted_pixels[0]["x"], sorted_pixels[0]["y"])
        count = 1
        
        for i in range(1, len(sorted_pixels)):
            pixel = sorted_pixels[i]
            
            if (pixel["color"] == current_color and 
                pixel["y"] == sorted_pixels[i-1]["y"] and 
                pixel["x"] == sorted_pixels[i-1]["x"] + 1):
                # نفس اللون ومتتالي أفقياً
                count += 1
            else:
                # لون مختلف أو غير متتالي
                if count == 1:
                    compressed.append({
                        "x": current_start[0],
                        "y": current_start[1],
                        "color": current_color
                    })
                else:
                    compressed.append({
                        "x": current_start[0],
                        "y": current_start[1],
                        "color": current_color,
                        "length": count
                    })
                
                current_color = pixel["color"]
                current_start = (pixel["x"], pixel["y"])
                count = 1
        
        # إضافة آخر مجموعة
        if count == 1:
            compressed.append({
                "x": current_start[0],
                "y": current_start[1],
                "color": current_color
            })
        else:
            compressed.append({
                "x": current_start[0],
                "y": current_start[1],
                "color": current_color,
                "length": count
            })
        
        return compressed
    
    def compress_by_rectangular_regions(self, pixel_map: List[Dict], 
                                      width: int, height: int) -> List[Dict]:
        """
        ضغط عبر تحديد المناطق المستطيلة
        """
        # إنشاء مصفوفة الألوان
        color_matrix = {}
        for pixel in pixel_map:
            color_matrix[(pixel["x"], pixel["y"])] = pixel["color"]
        
        compressed = []
        processed = set()
        
        for y in range(height):
            for x in range(width):
                if (x, y) in processed or (x, y) not in color_matrix:
                    continue
                
                current_color = color_matrix[(x, y)]
                
                # البحث عن أكبر مستطيل بنفس اللون
                max_width = 1
                max_height = 1
                
                # توسيع أفقياً
                while (x + max_width < width and 
                       (x + max_width, y) in color_matrix and
                       color_matrix[(x + max_width, y)] == current_color):
                    max_width += 1
                
                # توسيع عمودياً
                can_expand = True
                while (y + max_height < height and can_expand):
                    for dx in range(max_width):
                        if ((x + dx, y + max_height) not in color_matrix or
                            color_matrix[(x + dx, y + max_height)] != current_color):
                            can_expand = False
                            break
                    if can_expand:
                        max_height += 1
                
                # إضافة المنطقة
                if max_width == 1 and max_height == 1:
                    compressed.append({
                        "x": x,
                        "y": y,
                        "color": current_color
                    })
                else:
                    compressed.append({
                        "x": x,
                        "y": y,
                        "width": max_width,
                        "height": max_height,
                        "color": current_color
                    })
                
                # تسجيل البكسلات المعالجة
                for dy in range(max_height):
                    for dx in range(max_width):
                        processed.add((x + dx, y + dy))
        
        return compressed
    
    def decompress_pixel_map(self, compressed_map: List[Dict]) -> List[Dict]:
        """
        إلغاء ضغط خريطة البكسلات
        """
        decompressed = []
        
        for item in compressed_map:
            if "positions" in item:
                # مجموعة ألوان
                color = item["color"]
                for x, y in item["positions"]:
                    decompressed.append({
                        "x": x,
                        "y": y,
                        "color": color
                    })
            elif "length" in item:
                # Run-length encoding
                x, y = item["x"], item["y"]
                color = item["color"]
                length = item["length"]
                
                for i in range(length):
                    decompressed.append({
                        "x": x + i,
                        "y": y,
                        "color": color
                    })
            elif "width" in item and "height" in item:
                # منطقة مستطيلة
                x, y = item["x"], item["y"]
                width, height = item["width"], item["height"]
                color = item["color"]
                
                for dy in range(height):
                    for dx in range(width):
                        decompressed.append({
                            "x": x + dx,
                            "y": y + dy,
                            "color": color
                        })
            else:
                # بكسل عادي
                decompressed.append(item)
        
        return decompressed
    
    def calculate_compression_ratio(self, original: List[Dict], 
                                  compressed: List[Dict]) -> float:
        """
        حساب نسبة الضغط
        """
        original_size = len(json.dumps(original))
        compressed_size = len(json.dumps(compressed))
        
        if original_size == 0:
            return 0.0
        
        return (1 - compressed_size / original_size) * 100
