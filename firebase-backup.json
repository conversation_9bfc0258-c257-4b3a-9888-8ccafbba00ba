{"project": {"id": "image-encryption-2024", "name": "Image Encryption", "domain": "https://image-encryption-2024.web.app"}, "config": {"apiKey": "AIzaSyB8i6CE9z6BCw6OyCmNEtcNNDRt2nXMvjE", "authDomain": "image-encryption-2024.firebaseapp.com", "projectId": "image-encryption-2024", "storageBucket": "image-encryption-2024.firebasestorage.app", "messagingSenderId": "224377115630", "appId": "1:224377115630:web:c9c71e76a29670e776b4ff"}, "services": {"hosting": {"enabled": true, "domain": "https://image-encryption-2024.web.app"}, "firestore": {"enabled": true, "collections": ["encrypted-files", "usage-stats"]}, "storage": {"enabled": true, "buckets": ["encrypted-maps", "temp-images"]}, "analytics": {"enabled": true, "measurementId": "G-J12L13XPHZ"}}, "backup_date": "2024-01-01", "version": "1.0"}