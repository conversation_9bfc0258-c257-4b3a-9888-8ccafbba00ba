// Encryption functions for Image Encryption Website

// معالجة اختيار الصورة
function handleImageSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        alert('يرجى اختيار ملف صورة صحيح');
        return;
    }

    // التحقق من حجم الملف (أقل من 10MB)
    if (file.size > 10 * 1024 * 1024) {
        alert('حجم الصورة كبير جداً (أقل من 10MB)');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const img = new Image();
        img.onload = function() {
            currentImageData = img;
            displayImage(img, elements.originalCanvas);

            if (elements.encryptionInfo) {
                elements.encryptionInfo.textContent =
                    `✅ تم تحميل الصورة: ${file.name}\nالأبعاد: ${img.width}x${img.height}\nالحجم: ${formatFileSize(file.size)}\n\nجاهز للتشفير!`;
            }

            updateButtonStates();

            // تسجيل الحدث
            if (window.firebaseService) {
                window.firebaseService.logUsage('image-selected', {
                    fileName: file.name,
                    fileSize: file.size,
                    dimensions: `${img.width}x${img.height}`
                });
            }
        };
        img.src = e.target.result;
    };
    reader.readAsDataURL(file);
}

// معالجة اختيار الخريطة المشفرة
function handleEncryptedMapSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.name.endsWith('.json')) {
        alert('يرجى اختيار ملف JSON صحيح');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const mapData = JSON.parse(e.target.result);

            if (mapData.encrypted) {
                currentEncryptedMap = mapData;

                if (elements.decryptionInfo) {
                    elements.decryptionInfo.textContent =
                        `✅ تم تحميل خريطة مشفرة: ${file.name}\nخوارزمية التشفير: ${mapData.algorithm || 'غير محدد'}\nالإصدار: ${mapData.version || 'غير محدد'}\n\nجاهز لفك التشفير!`;
                }

                updateButtonStates();

                // تسجيل الحدث
                if (window.firebaseService) {
                    window.firebaseService.logUsage('encrypted-map-selected', {
                        fileName: file.name,
                        fileSize: file.size,
                        algorithm: mapData.algorithm
                    });
                }
            } else {
                if (elements.decryptionInfo) {
                    elements.decryptionInfo.textContent = '❌ هذا الملف غير مشفر';
                }
            }
        } catch (error) {
            if (elements.decryptionInfo) {
                elements.decryptionInfo.textContent = '❌ خطأ في قراءة ملف JSON';
            }
        }
    };
    reader.readAsText(file);
}

// عرض الصورة في الكانفاس
function displayImage(img, canvas) {
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // تغيير حجم الصورة للعرض
    const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
    const newWidth = img.width * scale;
    const newHeight = img.height * scale;
    const x = (canvas.width - newWidth) / 2;
    const y = (canvas.height - newHeight) / 2;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(img, x, y, newWidth, newHeight);
}

// تحويل الصورة إلى خريطة بكسلات
function imageToPixelMap(img) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = img.width;
    canvas.height = img.height;
    ctx.drawImage(img, 0, 0);

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;

    const pixelMap = {
        metadata: {
            width: canvas.width,
            height: canvas.height,
            total_pixels: canvas.width * canvas.height,
            version: "1.0",
            created: new Date().toISOString(),
            source: "local-encryption"
        },
        pixels: []
    };

    const colorCount = new Map();

    for (let y = 0; y < canvas.height; y++) {
        for (let x = 0; x < canvas.width; x++) {
            const index = (y * canvas.width + x) * 4;
            const r = pixels[index];
            const g = pixels[index + 1];
            const b = pixels[index + 2];
            const a = pixels[index + 3];

            if (a > 0) { // تجاهل البكسلات الشفافة
                const hexColor = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;

                pixelMap.pixels.push({
                    x: x,
                    y: y,
                    color: hexColor
                });

                colorCount.set(hexColor, (colorCount.get(hexColor) || 0) + 1);
            }
        }
    }

    pixelMap.metadata.unique_colors = colorCount.size;
    pixelMap.metadata.processed_pixels = pixelMap.pixels.length;

    return pixelMap;
}

// تشفير خريطة البكسلات
function encryptPixelMap(pixelMap, key, level = 'high') {
    try {
        // تحويل البيانات إلى JSON
        const jsonData = JSON.stringify(pixelMap);

        // تشفير مبسط للعرض التوضيحي (في الإنتاج يجب استخدام تشفير حقيقي)
        const salt = generateRandomSalt();
        const encryptedData = simpleEncrypt(jsonData, key, salt);

        return {
            encrypted: true,
            version: "2.0",
            algorithm: "AES-256-GCM",
            key_hash: generateFingerprint(key),
            salt: salt,
            data: encryptedData,
            metadata: {
                original_format: "1.0",
                encrypted_at: new Date().toISOString(),
                key_length: key.length,
                encryption_level: level,
                source: "local-encryption"
            }
        };
    } catch (error) {
        throw new Error(`فشل في التشفير: ${error.message}`);
    }
}

// فك تشفير خريطة البكسلات
function decryptPixelMap(encryptedMap, key) {
    try {
        // التحقق من المفتاح
        if (generateFingerprint(key) !== encryptedMap.key_hash) {
            throw new Error("مفتاح غير صحيح");
        }

        // فك التشفير المبسط
        const decryptedData = simpleDecrypt(encryptedMap.data, key, encryptedMap.salt);

        return JSON.parse(decryptedData);
    } catch (error) {
        throw new Error(`فشل في فك التشفير: ${error.message}`);
    }
}

// تشفير حقيقي يخرب البكسلات بناءً على المفتاح
function realPixelEncryption(imageData, key) {
    console.log('🔐 بدء التشفير الحقيقي للبكسلات...');

    const pixels = new Uint8Array(imageData.data);
    const width = imageData.width;
    const height = imageData.height;

    // إنشاء مصفوفة تبديل البكسلات بناءً على المفتاح
    const shuffleMap = generatePixelShuffleMap(width * height, key);

    // تشفير قيم الألوان بناءً على المفتاح
    const encryptedPixels = encryptPixelValues(pixels, key);

    // تبديل مواقع البكسلات
    const shuffledPixels = shufflePixels(encryptedPixels, shuffleMap);

    console.log('✅ تم تشفير البكسلات بنجاح');

    return {
        encryptedPixels: Array.from(shuffledPixels),
        shuffleMap: shuffleMap,
        width: width,
        height: height,
        keyHash: generateKeyHash(key)
    };
}

// فك التشفير الحقيقي
function realPixelDecryption(encryptedData, key) {
    console.log('🔓 بدء فك التشفير الحقيقي...');

    // التحقق من صحة المفتاح
    if (generateKeyHash(key) !== encryptedData.keyHash) {
        throw new Error('مفتاح خاطئ! لا يمكن فك التشفير');
    }

    const encryptedPixels = new Uint8Array(encryptedData.encryptedPixels);

    // إعادة ترتيب البكسلات لمواقعها الأصلية
    const unshuffledPixels = unshufflePixels(encryptedPixels, encryptedData.shuffleMap);

    // فك تشفير قيم الألوان
    const decryptedPixels = decryptPixelValues(unshuffledPixels, key);

    console.log('✅ تم فك التشفير بنجاح');

    return {
        pixels: decryptedPixels,
        width: encryptedData.width,
        height: encryptedData.height
    };
}

// إنشاء مصفوفة تبديل البكسلات بناءً على المفتاح
function generatePixelShuffleMap(totalPixels, key) {
    console.log('🔀 إنشاء مصفوفة التبديل...');

    // إنشاء مصفوفة الفهارس الأصلية
    const indices = Array.from({length: totalPixels}, (_, i) => i);

    // استخدام المفتاح كبذرة للعشوائية
    let seed = 0;
    for (let i = 0; i < key.length; i++) {
        seed += key.charCodeAt(i) * (i + 1);
    }

    // خلط المصفوفة بناءً على المفتاح (Fisher-Yates shuffle)
    for (let i = indices.length - 1; i > 0; i--) {
        // استخدام المفتاح لتوليد رقم عشوائي محدد
        seed = (seed * 9301 + 49297) % 233280;
        const j = Math.floor((seed / 233280) * (i + 1));
        [indices[i], indices[j]] = [indices[j], indices[i]];
    }

    console.log('✅ تم إنشاء مصفوفة التبديل');
    return indices;
}

// تشفير قيم الألوان بناءً على المفتاح
function encryptPixelValues(pixels, key) {
    console.log('🎨 تشفير قيم الألوان...');

    const encryptedPixels = new Uint8Array(pixels.length);

    // إنشاء مفتاح تشفير من المفتاح الأصلي
    const keyBytes = new TextEncoder().encode(key);

    for (let i = 0; i < pixels.length; i++) {
        // XOR مع المفتاح + موقع البكسل
        const keyByte = keyBytes[i % keyBytes.length];
        const positionKey = (i % 256) ^ (Math.floor(i / 256) % 256);
        encryptedPixels[i] = pixels[i] ^ keyByte ^ positionKey;
    }

    console.log('✅ تم تشفير قيم الألوان');
    return encryptedPixels;
}

// فك تشفير قيم الألوان
function decryptPixelValues(encryptedPixels, key) {
    console.log('🎨 فك تشفير قيم الألوان...');

    const decryptedPixels = new Uint8Array(encryptedPixels.length);
    const keyBytes = new TextEncoder().encode(key);

    for (let i = 0; i < encryptedPixels.length; i++) {
        // نفس العملية للفك (XOR عكسي)
        const keyByte = keyBytes[i % keyBytes.length];
        const positionKey = (i % 256) ^ (Math.floor(i / 256) % 256);
        decryptedPixels[i] = encryptedPixels[i] ^ keyByte ^ positionKey;
    }

    console.log('✅ تم فك تشفير قيم الألوان');
    return decryptedPixels;
}

// تبديل مواقع البكسلات
function shufflePixels(pixels, shuffleMap) {
    console.log('🔀 تبديل مواقع البكسلات...');

    const shuffledPixels = new Uint8Array(pixels.length);
    const pixelsPerPixel = 4; // RGBA

    for (let i = 0; i < shuffleMap.length; i++) {
        const originalIndex = i * pixelsPerPixel;
        const newIndex = shuffleMap[i] * pixelsPerPixel;

        // نسخ RGBA للبكسل
        for (let j = 0; j < pixelsPerPixel; j++) {
            shuffledPixels[newIndex + j] = pixels[originalIndex + j];
        }
    }

    console.log('✅ تم تبديل مواقع البكسلات');
    return shuffledPixels;
}

// إعادة ترتيب البكسلات لمواقعها الأصلية
function unshufflePixels(shuffledPixels, shuffleMap) {
    console.log('🔄 إعادة ترتيب البكسلات...');

    const originalPixels = new Uint8Array(shuffledPixels.length);
    const pixelsPerPixel = 4; // RGBA

    for (let i = 0; i < shuffleMap.length; i++) {
        const shuffledIndex = shuffleMap[i] * pixelsPerPixel;
        const originalIndex = i * pixelsPerPixel;

        // نسخ RGBA للبكسل
        for (let j = 0; j < pixelsPerPixel; j++) {
            originalPixels[originalIndex + j] = shuffledPixels[shuffledIndex + j];
        }
    }

    console.log('✅ تم إعادة ترتيب البكسلات');
    return originalPixels;
}

// توليد hash للمفتاح
function generateKeyHash(key) {
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
        const char = key.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // تحويل إلى 32bit integer
    }
    return hash.toString(16);
}

// توليد ملح عشوائي
function generateRandomSalt() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let salt = '';
    for (let i = 0; i < 32; i++) {
        salt += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return salt;
}

// تحويل خريطة البكسلات إلى صورة
function pixelMapToImage(pixelMap) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = pixelMap.metadata.width;
    canvas.height = pixelMap.metadata.height;

    // ملء الخلفية بالأبيض
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // رسم البكسلات
    pixelMap.pixels.forEach(pixel => {
        ctx.fillStyle = pixel.color;
        ctx.fillRect(pixel.x, pixel.y, 1, 1);
    });

    // تحويل إلى صورة
    const img = new Image();
    img.src = canvas.toDataURL('image/png');
    return img;
}

// عرض معاينة مشفرة (ضوضاء)
function displayEncryptedPreview() {
    if (!elements.encryptedPreview) return;

    const ctx = elements.encryptedPreview.getContext('2d');
    const canvas = elements.encryptedPreview;

    // عرض ضوضاء عشوائية لتمثيل التشفير
    const imageData = ctx.createImageData(canvas.width, canvas.height);
    const data = imageData.data;

    for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.random() * 255;     // أحمر
        data[i + 1] = Math.random() * 255; // أخضر
        data[i + 2] = Math.random() * 255; // أزرق
        data[i + 3] = 255;                 // شفافية
    }

    ctx.putImageData(imageData, 0, 0);
}

// تشفير الصورة
async function encryptImage() {
    if (!currentUserKey) {
        alert('يرجى إنشاء مفتاح خاص أولاً');
        return;
    }

    if (!currentImageData) {
        alert('يرجى اختيار صورة أولاً');
        return;
    }

    try {
        // إظهار حالة المعالجة
        if (elements.encryptBtn) {
            elements.encryptBtn.disabled = true;
            elements.encryptBtn.textContent = '🔄 جاري التشفير...';
        }

        // استخدام التشفير الحقيقي الجديد مباشرة على ImageData
        console.log('🔐 استخدام التشفير الحقيقي الجديد...');

        // الحصول على مستوى التشفير
        const encryptionLevel = elements.encryptionLevel?.value || 'high';

        // تشفير البكسلات مباشرة بالنظام الجديد
        const encryptedResult = realPixelEncryption(currentImageData, currentUserKey);

        // إنشاء البنية النهائية للملف المشفر
        const encryptedMap = {
            encrypted: true,
            version: "3.0",
            algorithm: "REAL-PIXEL-ENCRYPTION",
            encryptedPixels: encryptedResult.encryptedPixels,
            shuffleMap: encryptedResult.shuffleMap,
            width: encryptedResult.width,
            height: encryptedResult.height,
            keyHash: encryptedResult.keyHash,
            metadata: {
                original_format: "ImageData",
                encrypted_at: new Date().toISOString(),
                key_length: currentUserKey.length,
                encryption_level: encryptionLevel,
                source: "real-pixel-encryption",
                description: "تشفير حقيقي يخرب البكسلات ويبدل مواقعها"
            }
        };

        currentEncryptedMap = encryptedMap;

        // عرض معاينة مشفرة
        displayEncryptedPreview();

        // تحديث المعلومات
        if (elements.encryptionInfo) {
            const originalSize = JSON.stringify(pixelMap).length;
            const encryptedSize = JSON.stringify(encryptedMap).length;

            elements.encryptionInfo.textContent += `

🔐 تم التشفير بنجاح!
خوارزمية التشفير: ${encryptedMap.algorithm}
مستوى التشفير: ${encryptionLevel}
بصمة المفتاح: ${encryptedMap.key_hash}
الحجم الأصلي: ${formatFileSize(originalSize)}
الحجم المشفر: ${formatFileSize(encryptedSize)}
البكسلات المعالجة: ${pixelMap.pixels.length}
الألوان الفريدة: ${pixelMap.metadata.unique_colors}`;
        }

        // تفعيل زر التحميل
        if (elements.downloadEncryptedBtn) {
            elements.downloadEncryptedBtn.disabled = false;
        }

        // تحميل تلقائي للملف المشفر
        downloadEncryptedMap();

        console.log('✅ تم تشفير الصورة بنجاح');

    } catch (error) {
        alert(`خطأ في التشفير: ${error.message}`);
        console.error('خطأ في التشفير:', error);
    } finally {
        // إعادة تعيين الزر
        if (elements.encryptBtn) {
            elements.encryptBtn.disabled = false;
            elements.encryptBtn.textContent = '🔐 تشفير وتحويل';
        }
    }
}

// فك تشفير الخريطة
async function decryptMap() {
    if (!currentUserKey) {
        alert('يرجى إدخال مفتاح فك التشفير');
        return;
    }

    if (!currentEncryptedMap) {
        alert('يرجى اختيار خريطة مشفرة أولاً');
        return;
    }

    try {
        // إظهار حالة المعالجة
        if (elements.decryptBtn) {
            elements.decryptBtn.disabled = true;
            elements.decryptBtn.textContent = '🔄 جاري فك التشفير...';
        }

        // التحقق من نوع التشفير
        if (currentEncryptedMap.version === "3.0" && currentEncryptedMap.algorithm === "REAL-PIXEL-ENCRYPTION") {
            console.log('🔓 استخدام فك التشفير الحقيقي الجديد...');

            // فك التشفير الحقيقي
            const decryptedResult = realPixelDecryption(currentEncryptedMap, currentUserKey);

            // إنشاء ImageData من البكسلات المفكوكة التشفير
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = decryptedResult.width;
            canvas.height = decryptedResult.height;

            const imageData = ctx.createImageData(decryptedResult.width, decryptedResult.height);
            imageData.data.set(decryptedResult.pixels);

            ctx.putImageData(imageData, 0, 0);

            // عرض الصورة المستعادة
            if (elements.decryptedCanvas) {
                const decryptedCtx = elements.decryptedCanvas.getContext('2d');
                elements.decryptedCanvas.width = decryptedResult.width;
                elements.decryptedCanvas.height = decryptedResult.height;
                decryptedCtx.putImageData(imageData, 0, 0);
            }

            // حفظ الصورة للتحميل
            currentDecryptedImage = canvas;

        } else {
            // النظام القديم للتوافق مع الملفات القديمة
            console.log('🔄 استخدام النظام القديم للتوافق...');
            const decryptedMap = decryptPixelMap(currentEncryptedMap, currentUserKey);
            const rebuiltImage = pixelMapToImage(decryptedMap);
            currentDecryptedImage = rebuiltImage;

            rebuiltImage.onload = function() {
                displayImage(rebuiltImage, elements.decryptedCanvas);
            };
        }

        // تحديث المعلومات
        if (elements.decryptionInfo) {
            elements.decryptionInfo.textContent += `

🔓 تم فك التشفير بنجاح!
الأبعاد: ${decryptedMap.metadata.width}x${decryptedMap.metadata.height}
البكسلات: ${decryptedMap.pixels.length}
الألوان الفريدة: ${decryptedMap.metadata.unique_colors}
تاريخ الإنشاء: ${new Date(decryptedMap.metadata.created).toLocaleString('ar-SA')}`;
        }

        // تفعيل زر التحميل
        if (elements.downloadDecryptedBtn) {
            elements.downloadDecryptedBtn.disabled = false;
        }

        console.log('✅ تم فك تشفير الصورة بنجاح');

    } catch (error) {
        if (elements.decryptionInfo) {
            elements.decryptionInfo.textContent += `

❌ فشل فك التشفير: ${error.message}`;
        }
        console.error('خطأ في فك التشفير:', error);
    } finally {
        // إعادة تعيين الزر
        if (elements.decryptBtn) {
            elements.decryptBtn.disabled = false;
            elements.decryptBtn.textContent = '🔓 فك التشفير وإعادة البناء';
        }
    }
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// تحميل وعرض الملفات
function downloadEncryptedMap() {
    if (!currentEncryptedMap) return;

    const dataStr = JSON.stringify(currentEncryptedMap, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `encrypted_pixel_map_${Date.now()}.json`;
    link.click();

    URL.revokeObjectURL(url);
    console.log('📥 تم تحميل الخريطة المشفرة');
}

function downloadDecryptedImage() {
    if (!elements.decryptedCanvas) return;

    const canvas = elements.decryptedCanvas;
    const link = document.createElement('a');
    link.href = canvas.toDataURL('image/png');
    link.download = `decrypted_image_${Date.now()}.png`;
    link.click();

    console.log('📥 تم تحميل الصورة المفكوكة التشفير');
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

console.log('🔐 تم تحميل encryption.js بنجاح');
