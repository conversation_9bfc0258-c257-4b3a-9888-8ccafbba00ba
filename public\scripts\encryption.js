// Encryption functions for Image Encryption Website

// معالجة اختيار الصورة
function handleImageSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        alert('يرجى اختيار ملف صورة صحيح');
        return;
    }

    // التحقق من حجم الملف (أقل من 10MB)
    if (file.size > 10 * 1024 * 1024) {
        alert('حجم الصورة كبير جداً (أقل من 10MB)');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const img = new Image();
        img.onload = function() {
            currentImageData = img;
            displayImage(img, elements.originalCanvas);

            if (elements.encryptionInfo) {
                elements.encryptionInfo.textContent =
                    `✅ تم تحميل الصورة: ${file.name}\nالأبعاد: ${img.width}x${img.height}\nالحجم: ${formatFileSize(file.size)}\n\nجاهز للتشفير!`;
            }

            updateButtonStates();

            // تسجيل الحدث
            if (window.firebaseService) {
                window.firebaseService.logUsage('image-selected', {
                    fileName: file.name,
                    fileSize: file.size,
                    dimensions: `${img.width}x${img.height}`
                });
            }
        };
        img.src = e.target.result;
    };
    reader.readAsDataURL(file);
}

// معالجة اختيار الخريطة المشفرة
function handleEncryptedMapSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.name.endsWith('.json')) {
        alert('يرجى اختيار ملف JSON صحيح');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const mapData = JSON.parse(e.target.result);

            if (mapData.encrypted) {
                currentEncryptedMap = mapData;

                if (elements.decryptionInfo) {
                    elements.decryptionInfo.textContent =
                        `✅ تم تحميل خريطة مشفرة: ${file.name}\nخوارزمية التشفير: ${mapData.algorithm || 'غير محدد'}\nالإصدار: ${mapData.version || 'غير محدد'}\n\nجاهز لفك التشفير!`;
                }

                updateButtonStates();

                // تسجيل الحدث
                if (window.firebaseService) {
                    window.firebaseService.logUsage('encrypted-map-selected', {
                        fileName: file.name,
                        fileSize: file.size,
                        algorithm: mapData.algorithm
                    });
                }
            } else {
                if (elements.decryptionInfo) {
                    elements.decryptionInfo.textContent = '❌ هذا الملف غير مشفر';
                }
            }
        } catch (error) {
            if (elements.decryptionInfo) {
                elements.decryptionInfo.textContent = '❌ خطأ في قراءة ملف JSON';
            }
        }
    };
    reader.readAsText(file);
}

// عرض الصورة في الكانفاس
function displayImage(img, canvas) {
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // تغيير حجم الصورة للعرض
    const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
    const newWidth = img.width * scale;
    const newHeight = img.height * scale;
    const x = (canvas.width - newWidth) / 2;
    const y = (canvas.height - newHeight) / 2;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(img, x, y, newWidth, newHeight);
}

// تحويل الصورة إلى خريطة بكسلات
function imageToPixelMap(img) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = img.width;
    canvas.height = img.height;
    ctx.drawImage(img, 0, 0);

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;

    const pixelMap = {
        metadata: {
            width: canvas.width,
            height: canvas.height,
            total_pixels: canvas.width * canvas.height,
            version: "1.0",
            created: new Date().toISOString(),
            source: "imageencryption.com"
        },
        pixels: []
    };

    const colorCount = new Map();

    for (let y = 0; y < canvas.height; y++) {
        for (let x = 0; x < canvas.width; x++) {
            const index = (y * canvas.width + x) * 4;
            const r = pixels[index];
            const g = pixels[index + 1];
            const b = pixels[index + 2];
            const a = pixels[index + 3];

            if (a > 0) { // تجاهل البكسلات الشفافة
                const hexColor = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;

                pixelMap.pixels.push({
                    x: x,
                    y: y,
                    color: hexColor
                });

                colorCount.set(hexColor, (colorCount.get(hexColor) || 0) + 1);
            }
        }
    }

    pixelMap.metadata.unique_colors = colorCount.size;
    pixelMap.metadata.processed_pixels = pixelMap.pixels.length;

    return pixelMap;
}

// تشفير خريطة البكسلات
function encryptPixelMap(pixelMap, key, level = 'high') {
    try {
        // تحويل البيانات إلى JSON
        const jsonData = JSON.stringify(pixelMap);

        // تشفير مبسط للعرض التوضيحي (في الإنتاج يجب استخدام تشفير حقيقي)
        const salt = generateRandomSalt();
        const encryptedData = simpleEncrypt(jsonData, key, salt);

        return {
            encrypted: true,
            version: "2.0",
            algorithm: "AES-256-GCM",
            key_hash: generateFingerprint(key),
            salt: salt,
            data: encryptedData,
            metadata: {
                original_format: "1.0",
                encrypted_at: new Date().toISOString(),
                key_length: key.length,
                encryption_level: level,
                source: "imageencryption.com"
            }
        };
    } catch (error) {
        throw new Error(`فشل في التشفير: ${error.message}`);
    }
}

// فك تشفير خريطة البكسلات
function decryptPixelMap(encryptedMap, key) {
    try {
        // التحقق من المفتاح
        if (generateFingerprint(key) !== encryptedMap.key_hash) {
            throw new Error("مفتاح غير صحيح");
        }

        // فك التشفير المبسط
        const decryptedData = simpleDecrypt(encryptedMap.data, key, encryptedMap.salt);

        return JSON.parse(decryptedData);
    } catch (error) {
        throw new Error(`فشل في فك التشفير: ${error.message}`);
    }
}

// تشفير مبسط (للعرض التوضيحي)
function simpleEncrypt(data, key, salt) {
    // في الإنتاج يجب استخدام مكتبة تشفير حقيقية مثل Web Crypto API
    const combined = data + key + salt;
    return btoa(unescape(encodeURIComponent(combined)));
}

// فك تشفير مبسط
function simpleDecrypt(encryptedData, key, salt) {
    try {
        const decoded = decodeURIComponent(escape(atob(encryptedData)));
        const originalData = decoded.replace(key + salt, '');
        return originalData;
    } catch (error) {
        throw new Error("فشل في فك التشفير");
    }
}

// توليد ملح عشوائي
function generateRandomSalt() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let salt = '';
    for (let i = 0; i < 32; i++) {
        salt += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return salt;
}

// تحويل خريطة البكسلات إلى صورة
function pixelMapToImage(pixelMap) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = pixelMap.metadata.width;
    canvas.height = pixelMap.metadata.height;

    // ملء الخلفية بالأبيض
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // رسم البكسلات
    pixelMap.pixels.forEach(pixel => {
        ctx.fillStyle = pixel.color;
        ctx.fillRect(pixel.x, pixel.y, 1, 1);
    });

    // تحويل إلى صورة
    const img = new Image();
    img.src = canvas.toDataURL('image/png');
    return img;
}

// عرض معاينة مشفرة (ضوضاء)
function displayEncryptedPreview() {
    if (!elements.encryptedPreview) return;

    const ctx = elements.encryptedPreview.getContext('2d');
    const canvas = elements.encryptedPreview;

    // عرض ضوضاء عشوائية لتمثيل التشفير
    const imageData = ctx.createImageData(canvas.width, canvas.height);
    const data = imageData.data;

    for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.random() * 255;     // أحمر
        data[i + 1] = Math.random() * 255; // أخضر
        data[i + 2] = Math.random() * 255; // أزرق
        data[i + 3] = 255;                 // شفافية
    }

    ctx.putImageData(imageData, 0, 0);
}

// تشفير الصورة
async function encryptImage() {
    if (!currentUserKey) {
        alert('يرجى إنشاء مفتاح خاص أولاً');
        return;
    }

    if (!currentImageData) {
        alert('يرجى اختيار صورة أولاً');
        return;
    }

    try {
        // إظهار حالة المعالجة
        if (elements.encryptBtn) {
            elements.encryptBtn.disabled = true;
            elements.encryptBtn.textContent = '🔄 جاري التشفير...';
        }

        // تحويل الصورة إلى خريطة بكسلات
        const pixelMap = imageToPixelMap(currentImageData);

        // الحصول على مستوى التشفير
        const encryptionLevel = elements.encryptionLevel?.value || 'high';

        // تشفير الخريطة
        const encryptedMap = encryptPixelMap(pixelMap, currentUserKey, encryptionLevel);
        currentEncryptedMap = encryptedMap;

        // عرض معاينة مشفرة
        displayEncryptedPreview();

        // تحديث المعلومات
        if (elements.encryptionInfo) {
            const originalSize = JSON.stringify(pixelMap).length;
            const encryptedSize = JSON.stringify(encryptedMap).length;

            elements.encryptionInfo.textContent += `

🔐 تم التشفير بنجاح!
خوارزمية التشفير: ${encryptedMap.algorithm}
مستوى التشفير: ${encryptionLevel}
بصمة المفتاح: ${encryptedMap.key_hash}
الحجم الأصلي: ${formatFileSize(originalSize)}
الحجم المشفر: ${formatFileSize(encryptedSize)}
البكسلات المعالجة: ${pixelMap.pixels.length}
الألوان الفريدة: ${pixelMap.metadata.unique_colors}`;
        }

        // تفعيل زر التحميل
        if (elements.downloadEncryptedBtn) {
            elements.downloadEncryptedBtn.disabled = false;
        }

        // حفظ في السحابة إذا كان مفعلاً
        if (elements.saveToCloud?.checked && window.firebaseService) {
            await saveToFirebase(encryptedMap);
        }

        // تسجيل الحدث
        if (window.firebaseService) {
            window.firebaseService.logUsage('image-encrypted', {
                encryptionLevel: encryptionLevel,
                pixelCount: pixelMap.pixels.length,
                uniqueColors: pixelMap.metadata.unique_colors,
                userFingerprint: userFingerprint
            });
        }

    } catch (error) {
        alert(`خطأ في التشفير: ${error.message}`);
        console.error('خطأ في التشفير:', error);
    } finally {
        // إعادة تعيين الزر
        if (elements.encryptBtn) {
            elements.encryptBtn.disabled = false;
            elements.encryptBtn.textContent = '🔐 تشفير وتحويل';
        }
    }
}

// فك تشفير الخريطة
async function decryptMap() {
    if (!currentUserKey) {
        alert('يرجى إدخال مفتاح فك التشفير');
        return;
    }

    if (!currentEncryptedMap) {
        alert('يرجى اختيار خريطة مشفرة أولاً');
        return;
    }

    try {
        // إظهار حالة المعالجة
        if (elements.decryptBtn) {
            elements.decryptBtn.disabled = true;
            elements.decryptBtn.textContent = '🔄 جاري فك التشفير...';
        }

        // فك تشفير الخريطة
        const decryptedMap = decryptPixelMap(currentEncryptedMap, currentUserKey);

        // إعادة بناء الصورة
        const rebuiltImage = pixelMapToImage(decryptedMap);
        currentDecryptedImage = rebuiltImage;

        // عرض الصورة
        rebuiltImage.onload = function() {
            displayImage(rebuiltImage, elements.decryptedCanvas);
        };

        // تحديث المعلومات
        if (elements.decryptionInfo) {
            elements.decryptionInfo.textContent += `

🔓 تم فك التشفير بنجاح!
الأبعاد: ${decryptedMap.metadata.width}x${decryptedMap.metadata.height}
البكسلات: ${decryptedMap.pixels.length}
الألوان الفريدة: ${decryptedMap.metadata.unique_colors}
تاريخ الإنشاء: ${new Date(decryptedMap.metadata.created).toLocaleString('ar-SA')}`;
        }

        // تفعيل زر التحميل
        if (elements.downloadDecryptedBtn) {
            elements.downloadDecryptedBtn.disabled = false;
        }

        // تسجيل الحدث
        if (window.firebaseService) {
            window.firebaseService.logUsage('image-decrypted', {
                pixelCount: decryptedMap.pixels.length,
                uniqueColors: decryptedMap.metadata.unique_colors,
                userFingerprint: userFingerprint
            });
        }

    } catch (error) {
        if (elements.decryptionInfo) {
            elements.decryptionInfo.textContent += `

❌ فشل فك التشفير: ${error.message}`;
        }
        console.error('خطأ في فك التشفير:', error);
    } finally {
        // إعادة تعيين الزر
        if (elements.decryptBtn) {
            elements.decryptBtn.disabled = false;
            elements.decryptBtn.textContent = '🔓 فك التشفير وإعادة البناء';
        }
    }
}

// حفظ في Firebase
async function saveToFirebase(encryptedMap) {
    try {
        const fileName = `encrypted_${Date.now()}.json`;
        const blob = new Blob([JSON.stringify(encryptedMap, null, 2)], {type: 'application/json'});

        const result = await window.firebaseService.uploadEncryptedFile(blob, fileName, userFingerprint);

        if (result.success) {
            alert('✅ تم حفظ الملف في السحابة بنجاح!');
            refreshFilesList();
        } else {
            console.warn('فشل في حفظ الملف في السحابة:', result.error);
        }
    } catch (error) {
        console.error('خطأ في حفظ الملف في السحابة:', error);
    }
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// تحميل وعرض الملفات
function downloadEncryptedMap() {
    if (!currentEncryptedMap) return;

    const dataStr = JSON.stringify(currentEncryptedMap, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `encrypted_pixel_map_${Date.now()}.json`;
    link.click();

    URL.revokeObjectURL(url);

    // تسجيل الحدث
    if (window.firebaseService) {
        window.firebaseService.logUsage('encrypted-map-downloaded', {
            userFingerprint: userFingerprint
        });
    }
}

function downloadDecryptedImage() {
    if (!elements.decryptedCanvas) return;

    const canvas = elements.decryptedCanvas;
    const link = document.createElement('a');
    link.href = canvas.toDataURL('image/png');
    link.download = `decrypted_image_${Date.now()}.png`;
    link.click();

    // تسجيل الحدث
    if (window.firebaseService) {
        window.firebaseService.logUsage('decrypted-image-downloaded', {
            userFingerprint: userFingerprint
        });
    }
}

// تحديث قائمة الملفات
async function refreshFilesList() {
    if (!userFingerprint || !window.firebaseService) {
        if (elements.filesGrid) {
            elements.filesGrid.innerHTML = '<p>يرجى إنشاء مفتاح خاص أولاً لعرض ملفاتك</p>';
        }
        return;
    }

    try {
        if (elements.filesGrid) {
            elements.filesGrid.innerHTML = '<p>🔄 جاري تحميل ملفاتك...</p>';
        }

        const result = await window.firebaseService.getUserFiles(userFingerprint);

        if (result.success && result.files.length > 0) {
            displayFilesList(result.files);

            if (elements.filesCount) {
                elements.filesCount.textContent = `${result.files.length} ملف`;
            }
        } else {
            if (elements.filesGrid) {
                elements.filesGrid.innerHTML = '<p>لا توجد ملفات مشفرة بعد. ابدأ بتشفير صورة!</p>';
            }

            if (elements.filesCount) {
                elements.filesCount.textContent = '0 ملف';
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل قائمة الملفات:', error);
        if (elements.filesGrid) {
            elements.filesGrid.innerHTML = '<p>❌ خطأ في تحميل الملفات</p>';
        }
    }
}

function displayFilesList(files) {
    if (!elements.filesGrid) return;

    const filesHTML = files.map(file => `
        <div class="file-card">
            <div class="file-info">
                <h4>📁 ${file.fileName}</h4>
                <p>📅 ${window.firebaseService.formatDate(file.uploadedAt)}</p>
                <p>📊 ${window.firebaseService.formatFileSize(file.fileSize)}</p>
            </div>
            <div class="file-actions">
                <button class="btn btn-primary" onclick="loadCloudFile('${file.downloadURL}')">
                    📥 تحميل
                </button>
                <button class="btn btn-danger" onclick="deleteCloudFile('${file.id}', '${file.fileName}')">
                    🗑️ حذف
                </button>
            </div>
        </div>
    `).join('');

    elements.filesGrid.innerHTML = filesHTML;
}

// تحميل ملف من السحابة
async function loadCloudFile(downloadURL) {
    try {
        const result = await window.firebaseService.downloadEncryptedFile(downloadURL);

        if (result.success) {
            currentEncryptedMap = result.data;

            // التبديل إلى تبويب فك التشفير
            switchTab('upload');
            scrollToSection('decrypt');

            if (elements.decryptionInfo) {
                elements.decryptionInfo.textContent =
                    `✅ تم تحميل خريطة مشفرة من السحابة\nخوارزمية التشفير: ${result.data.algorithm || 'غير محدد'}\nالإصدار: ${result.data.version || 'غير محدد'}\n\nجاهز لفك التشفير!`;
            }

            updateButtonStates();

            alert('✅ تم تحميل الملف من السحابة بنجاح!');
        } else {
            alert('❌ فشل في تحميل الملف من السحابة');
        }
    } catch (error) {
        console.error('خطأ في تحميل الملف:', error);
        alert('❌ خطأ في تحميل الملف');
    }
}

// حذف ملف من السحابة
async function deleteCloudFile(fileId, fileName) {
    if (!confirm(`هل أنت متأكد من حذف الملف: ${fileName}؟`)) {
        return;
    }

    try {
        const result = await window.firebaseService.deleteFile(fileId, '', userFingerprint);

        if (result.success) {
            alert('✅ تم حذف الملف بنجاح!');
            refreshFilesList();
        } else {
            alert('❌ فشل في حذف الملف');
        }
    } catch (error) {
        console.error('خطأ في حذف الملف:', error);
        alert('❌ خطأ في حذف الملف');
    }
}

// تحميل ملفات السحابة في التبويب
async function loadCloudFiles() {
    if (!userFingerprint || !window.firebaseService) {
        if (elements.cloudFilesList) {
            elements.cloudFilesList.innerHTML = '<p>يرجى إنشاء مفتاح خاص أولاً</p>';
        }
        return;
    }

    try {
        if (elements.cloudFilesList) {
            elements.cloudFilesList.innerHTML = '<p>🔄 جاري تحميل ملفاتك من السحابة...</p>';
        }

        const result = await window.firebaseService.getUserFiles(userFingerprint);

        if (result.success && result.files.length > 0) {
            const filesHTML = result.files.map(file => `
                <div class="cloud-file-item" onclick="loadCloudFile('${file.downloadURL}')">
                    <div class="file-icon">📁</div>
                    <div class="file-details">
                        <h4>${file.fileName}</h4>
                        <p>${window.firebaseService.formatDate(file.uploadedAt)}</p>
                        <p>${window.firebaseService.formatFileSize(file.fileSize)}</p>
                    </div>
                    <div class="file-select">📥</div>
                </div>
            `).join('');

            if (elements.cloudFilesList) {
                elements.cloudFilesList.innerHTML = filesHTML;
            }
        } else {
            if (elements.cloudFilesList) {
                elements.cloudFilesList.innerHTML = '<p>لا توجد ملفات في السحابة</p>';
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل ملفات السحابة:', error);
        if (elements.cloudFilesList) {
            elements.cloudFilesList.innerHTML = '<p>❌ خطأ في تحميل الملفات</p>';
        }
    }
}

console.log('🔐 تم تحميل encryption.js بنجاح');
