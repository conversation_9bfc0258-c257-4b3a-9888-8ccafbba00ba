{"hosting": {"site": "fvslygdluy", "public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(html|json)", "headers": [{"key": "Cache-Control", "value": "max-age=3600"}]}], "cleanUrls": true, "trailingSlash": false}, "storage": {"rules": "storage.rules"}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}}