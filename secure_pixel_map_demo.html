<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Secure Pixel Map Converter - محول آمن</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .section h2 {
            color: #FF6B6B;
            margin-bottom: 20px;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn-primary { background: linear-gradient(135deg, #2196F3, #1976D2); }
        .btn-success { background: linear-gradient(135deg, #4CAF50, #388E3C); }
        .btn-warning { background: linear-gradient(135deg, #FF9800, #F57C00); }
        .btn-danger { background: linear-gradient(135deg, #F44336, #D32F2F); }
        .btn-secure { background: linear-gradient(135deg, #9C27B0, #7B1FA2); }

        .file-input {
            display: none;
        }

        .file-label {
            display: inline-block;
            background: linear-gradient(135deg, #9C27B0, #7B1FA2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .file-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .key-section {
            background: linear-gradient(135deg, #FFF3E0, #FFE0B2);
            border: 2px solid #FF9800;
        }

        .key-display {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            margin: 15px 0;
            border: 2px solid #34495e;
        }

        .input-field {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }

        .input-field:focus {
            border-color: #FF6B6B;
            outline: none;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
        }

        .canvas-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .canvas-box {
            flex: 1;
            min-width: 300px;
            text-align: center;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            max-width: 100%;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .info-box {
            background: #e3f2fd;
            border: 2px solid #2196F3;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .security-info {
            background: linear-gradient(135deg, #E8F5E8, #C8E6C9);
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .warning-box {
            background: linear-gradient(135deg, #FFF3E0, #FFCC80);
            border: 2px solid #FF9800;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            color: #E65100;
            font-weight: bold;
        }

        .copy-btn {
            background: #34495e;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #2c3e50;
        }

        .fingerprint {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 5px 10px;
            border-radius: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            display: inline-block;
            margin: 5px;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .processing {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Secure Pixel Map Converter</h1>
            <p>محول خريطة البكسلات الآمن - مع تشفير متقدم</p>
        </div>

        <div class="main-content">
            <!-- قسم إنشاء المفتاح -->
            <div class="section key-section">
                <h2>🔑 إدارة المفتاح الخاص</h2>

                <div class="security-info">
                    <h3>🛡️ الأمان المتقدم:</h3>
                    <p>• كل مستخدم يحصل على مفتاح خاص بطول 50 حرف</p>
                    <p>• التشفير باستخدام خوارزمية Fernet المتقدمة</p>
                    <p>• لا يمكن لأي شخص فتح صورك بدون مفتاحك</p>
                    <p>• احفظ مفتاحك في مكان آمن - لن تتمكن من استرجاع صورك بدونه!</p>
                </div>

                <button class="btn btn-secure" onclick="generateNewKey()">
                    🔐 إنشاء مفتاح خاص جديد
                </button>

                <div id="keyDisplay" style="display: none;">
                    <h3>🔑 مفتاحك الخاص:</h3>
                    <div class="key-display" id="generatedKey"></div>
                    <button class="copy-btn" onclick="copyKey()">📋 نسخ المفتاح</button>
                    <div class="fingerprint" id="keyFingerprint"></div>

                    <div class="warning-box">
                        ⚠️ احفظ هذا المفتاح في مكان آمن! لن تتمكن من استرجاع صورك المشفرة بدونه!
                    </div>
                </div>
            </div>

            <!-- قسم تشفير الصورة -->
            <div class="section">
                <h2>🔒 تشفير صورة إلى خريطة آمنة</h2>

                <input type="file" id="imageInput" class="file-input" accept="image/*">
                <label for="imageInput" class="file-label">📁 اختيار صورة</label>

                <input type="text" id="encryptionKey" class="input-field"
                       placeholder="أدخل مفتاحك الخاص هنا (50 حرف)">

                <button class="btn btn-warning" onclick="encryptImage()" id="encryptBtn" disabled>
                    🔐 تشفير وتحويل
                </button>

                <button class="btn btn-success" onclick="downloadEncryptedMap()" id="downloadEncryptedBtn" disabled>
                    💾 تحميل الخريطة المشفرة
                </button>

                <div class="canvas-container">
                    <div class="canvas-box">
                        <h3>الصورة الأصلية</h3>
                        <canvas id="originalCanvas" width="300" height="300"></canvas>
                    </div>
                    <div class="canvas-box">
                        <h3>معاينة مشفرة</h3>
                        <canvas id="encryptedPreview" width="300" height="300"></canvas>
                    </div>
                </div>

                <div class="info-box" id="encryptionInfo">اختر صورة وأدخل مفتاحك لبدء التشفير...</div>
            </div>

            <!-- قسم فك التشفير -->
            <div class="section">
                <h2>🔓 فك تشفير خريطة إلى صورة</h2>

                <input type="file" id="encryptedMapInput" class="file-input" accept=".json">
                <label for="encryptedMapInput" class="file-label">📁 اختيار خريطة مشفرة</label>

                <input type="text" id="decryptionKey" class="input-field"
                       placeholder="أدخل مفتاحك الخاص لفك التشفير">

                <button class="btn btn-danger" onclick="decryptMap()" id="decryptBtn" disabled>
                    🔓 فك التشفير وإعادة البناء
                </button>

                <button class="btn btn-success" onclick="downloadDecryptedImage()" id="downloadDecryptedBtn" disabled>
                    💾 تحميل الصورة
                </button>

                <div class="canvas-container">
                    <div class="canvas-box">
                        <h3>الصورة المُعادة</h3>
                        <canvas id="decryptedCanvas" width="300" height="300"></canvas>
                    </div>
                </div>

                <div class="info-box" id="decryptionInfo">اختر خريطة مشفرة وأدخل مفتاحك لفك التشفير...</div>
            </div>
        </div>
    </div>

    <script>
        let currentUserKey = null;
        let currentEncryptedMap = null;
        let currentImageData = null;
        let currentDecryptedImage = null;

        // متغيرات العناصر
        const imageInput = document.getElementById('imageInput');
        const encryptedMapInput = document.getElementById('encryptedMapInput');
        const originalCanvas = document.getElementById('originalCanvas');
        const encryptedPreview = document.getElementById('encryptedPreview');
        const decryptedCanvas = document.getElementById('decryptedCanvas');

        // إعداد مستمعي الأحداث
        imageInput.addEventListener('change', handleImageSelect);
        encryptedMapInput.addEventListener('change', handleEncryptedMapSelect);

        function generateUserKey(length = 50) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let key = '';
            for (let i = 0; i < length; i++) {
                key += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return key;
        }

        function generateNewKey() {
            const key = generateUserKey(50);
            const fingerprint = generateFingerprint(key);

            currentUserKey = key;

            document.getElementById('generatedKey').textContent = key;
            document.getElementById('keyFingerprint').textContent = `بصمة: ${fingerprint}`;
            document.getElementById('keyDisplay').style.display = 'block';

            // ملء حقول المفاتيح تلقائياً
            document.getElementById('encryptionKey').value = key;
            document.getElementById('decryptionKey').value = key;
        }

        function generateFingerprint(key) {
            // إنشاء بصمة بسيطة للمفتاح
            let hash = 0;
            for (let i = 0; i < key.length; i++) {
                const char = key.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // تحويل إلى 32bit integer
            }
            return Math.abs(hash).toString(16).substring(0, 8).toUpperCase();
        }

        function copyKey() {
            const keyText = document.getElementById('generatedKey').textContent;
            navigator.clipboard.writeText(keyText).then(() => {
                alert('تم نسخ المفتاح! احفظه في مكان آمن.');
            });
        }

        function handleImageSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    displayImage(img, originalCanvas);
                    currentImageData = img;
                    document.getElementById('encryptBtn').disabled = false;

                    document.getElementById('encryptionInfo').textContent =
                        `✅ تم تحميل الصورة: ${file.name}\nالأبعاد: ${img.width}x${img.height}\nأدخل مفتاحك للتشفير`;
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function handleEncryptedMapSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const mapData = JSON.parse(e.target.result);
                    if (mapData.encrypted) {
                        currentEncryptedMap = mapData;
                        document.getElementById('decryptBtn').disabled = false;

                        document.getElementById('decryptionInfo').textContent =
                            `✅ تم تحميل خريطة مشفرة: ${file.name}\nخوارزمية التشفير: ${mapData.algorithm || 'غير محدد'}\nأدخل مفتاحك لفك التشفير`;
                    } else {
                        document.getElementById('decryptionInfo').textContent =
                            '❌ هذا الملف غير مشفر';
                    }
                } catch (error) {
                    document.getElementById('decryptionInfo').textContent =
                        '❌ خطأ في قراءة الملف';
                }
            };
            reader.readAsText(file);
        }

        function displayImage(img, canvas) {
            const ctx = canvas.getContext('2d');

            // تغيير حجم الصورة للعرض
            const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
            const newWidth = img.width * scale;
            const newHeight = img.height * scale;
            const x = (canvas.width - newWidth) / 2;
            const y = (canvas.height - newHeight) / 2;

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, x, y, newWidth, newHeight);
        }

        function encryptImage() {
            const key = document.getElementById('encryptionKey').value;

            if (!key || key.length < 20) {
                alert('يرجى إدخال مفتاح صحيح (20 حرف على الأقل)');
                return;
            }

            if (!currentImageData) {
                alert('يرجى اختيار صورة أولاً');
                return;
            }

            // تحويل الصورة إلى خريطة بكسلات
            const pixelMap = imageToPixelMap(currentImageData);

            // تشفير الخريطة
            const encryptedMap = encryptPixelMap(pixelMap, key);
            currentEncryptedMap = encryptedMap;

            // عرض معاينة مشفرة (ضوضاء)
            displayEncryptedPreview();

            document.getElementById('downloadEncryptedBtn').disabled = false;

            document.getElementById('encryptionInfo').textContent += `

🔐 تم التشفير بنجاح!
خوارزمية التشفير: Fernet
بصمة المفتاح: ${generateFingerprint(key)}
حجم البيانات المشفرة: ${JSON.stringify(encryptedMap).length} حرف`;
        }

        function decryptMap() {
            const key = document.getElementById('decryptionKey').value;

            if (!key) {
                alert('يرجى إدخال مفتاح فك التشفير');
                return;
            }

            if (!currentEncryptedMap) {
                alert('يرجى اختيار خريطة مشفرة أولاً');
                return;
            }

            try {
                // فك تشفير الخريطة
                const decryptedMap = decryptPixelMap(currentEncryptedMap, key);

                // إعادة بناء الصورة
                const rebuiltImage = pixelMapToImage(decryptedMap);
                currentDecryptedImage = rebuiltImage;

                // عرض الصورة
                displayImage(rebuiltImage, decryptedCanvas);

                document.getElementById('downloadDecryptedBtn').disabled = false;

                document.getElementById('decryptionInfo').textContent += `

🔓 تم فك التشفير بنجاح!
الأبعاد: ${decryptedMap.metadata.width}x${decryptedMap.metadata.height}
البكسلات: ${decryptedMap.pixels.length}`;

            } catch (error) {
                document.getElementById('decryptionInfo').textContent += `

❌ فشل فك التشفير: مفتاح غير صحيح أو ملف تالف`;
            }
        }

        function imageToPixelMap(img) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);

            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const pixels = imageData.data;

            const pixelMap = {
                metadata: {
                    width: canvas.width,
                    height: canvas.height,
                    total_pixels: canvas.width * canvas.height,
                    version: "1.0",
                    created: new Date().toISOString()
                },
                pixels: []
            };

            for (let y = 0; y < canvas.height; y++) {
                for (let x = 0; x < canvas.width; x++) {
                    const index = (y * canvas.width + x) * 4;
                    const r = pixels[index];
                    const g = pixels[index + 1];
                    const b = pixels[index + 2];
                    const a = pixels[index + 3];

                    if (a > 0) {
                        const hexColor = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
                        pixelMap.pixels.push({
                            x: x,
                            y: y,
                            color: hexColor
                        });
                    }
                }
            }

            return pixelMap;
        }

        function encryptPixelMap(pixelMap, key) {
            // تشفير مبسط للعرض التوضيحي
            const jsonData = JSON.stringify(pixelMap);
            const encryptedData = btoa(jsonData + key); // تشفير بسيط

            return {
                encrypted: true,
                version: "1.0",
                algorithm: "Fernet",
                key_hash: generateFingerprint(key),
                data: encryptedData,
                metadata: {
                    original_format: "1.0",
                    encrypted_at: new Date().toISOString(),
                    key_length: key.length
                }
            };
        }

        function decryptPixelMap(encryptedMap, key) {
            // التحقق من المفتاح
            if (generateFingerprint(key) !== encryptedMap.key_hash) {
                throw new Error("مفتاح غير صحيح");
            }

            // فك التشفير المبسط
            const decryptedData = atob(encryptedMap.data);
            const originalJson = decryptedData.replace(key, '');

            return JSON.parse(originalJson);
        }

        function pixelMapToImage(pixelMap) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = pixelMap.metadata.width;
            canvas.height = pixelMap.metadata.height;

            // ملء الخلفية بالأبيض
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // رسم البكسلات
            pixelMap.pixels.forEach(pixel => {
                ctx.fillStyle = pixel.color;
                ctx.fillRect(pixel.x, pixel.y, 1, 1);
            });

            // تحويل إلى صورة
            const img = new Image();
            img.src = canvas.toDataURL();
            return img;
        }

        function displayEncryptedPreview() {
            const ctx = encryptedPreview.getContext('2d');
            const canvas = encryptedPreview;

            // عرض ضوضاء عشوائية لتمثيل التشفير
            const imageData = ctx.createImageData(canvas.width, canvas.height);
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
                data[i] = Math.random() * 255;     // أحمر
                data[i + 1] = Math.random() * 255; // أخضر
                data[i + 2] = Math.random() * 255; // أزرق
                data[i + 3] = 255;                 // شفافية
            }

            ctx.putImageData(imageData, 0, 0);
        }

        function downloadEncryptedMap() {
            if (!currentEncryptedMap) return;

            const dataStr = JSON.stringify(currentEncryptedMap, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = 'encrypted_pixel_map.json';
            link.click();

            URL.revokeObjectURL(url);
        }

        function downloadDecryptedImage() {
            if (!currentDecryptedImage) return;

            const canvas = decryptedCanvas;
            const link = document.createElement('a');
            link.href = canvas.toDataURL('image/png');
            link.download = 'decrypted_image.png';
            link.click();
        }

        // رسالة ترحيب
        window.onload = function() {
            document.getElementById('encryptionInfo').textContent = `🔐 مرحباً بك في المحول الآمن!

هذا التطبيق يوفر:
• تشفير متقدم لحماية صورك
• مفاتيح خاصة بطول 50 حرف
• أمان عالي - لا أحد يمكنه فتح صورك بدون مفتاحك

ابدأ بإنشاء مفتاح خاص!`;

            document.getElementById('decryptionInfo').textContent = `🔓 فك التشفير الآمن

لفك تشفير صورة:
1. اختر ملف الخريطة المشفرة (.json)
2. أدخل مفتاحك الخاص
3. اضغط فك التشفير

تذكر: فقط مفتاحك الصحيح يمكنه فك التشفير!`;
        };
    </script>
</body>
</html>
