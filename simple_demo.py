#!/usr/bin/env python3
"""
عرض توضيحي بسيط لمفهوم تحويل الصور إلى خريطة بكسلات
بدون استخدام مكتبات خارجية - للفهم فقط
"""

import json
import os

def create_simple_pixel_map():
    """إنشاء خريطة بكسلات بسيطة يدوياً"""
    
    print("🎨 إنشاء خريطة بكسلات بسيطة (3x3)...")
    
    # تعريف صورة بسيطة 3x3 بكسل
    # R = أحمر، G = أخضر، B = أزرق، W = أبيض
    simple_image = [
        ['R', 'G', 'B'],
        ['W', 'R', 'G'], 
        ['B', 'W', 'R']
    ]
    
    # تحويل الألوان إلى hex
    color_map = {
        'R': '#ff0000',  # أحمر
        'G': '#00ff00',  # أخضر
        'B': '#0000ff',  # أزرق
        'W': '#ffffff'   # أبيض
    }
    
    # إنشاء خريطة البكسلات
    pixel_map = {
        "metadata": {
            "width": 3,
            "height": 3,
            "total_pixels": 9,
            "description": "صورة تجريبية بسيطة 3x3",
            "version": "1.0"
        },
        "pixels": []
    }
    
    # تحويل المصفوفة إلى خريطة بكسلات
    for y in range(3):
        for x in range(3):
            color_letter = simple_image[y][x]
            hex_color = color_map[color_letter]
            
            pixel_map["pixels"].append({
                "x": x,
                "y": y,
                "color": hex_color
            })
    
    return pixel_map

def save_pixel_map(pixel_map, filename):
    """حفظ خريطة البكسلات في ملف JSON"""
    
    os.makedirs("output", exist_ok=True)
    filepath = os.path.join("output", filename)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(pixel_map, f, indent=2, ensure_ascii=False)
    
    print(f"✅ تم حفظ خريطة البكسلات: {filepath}")
    return filepath

def load_pixel_map(filepath):
    """تحميل خريطة البكسلات من ملف JSON"""
    
    with open(filepath, 'r', encoding='utf-8') as f:
        return json.load(f)

def display_pixel_map_as_text(pixel_map):
    """عرض خريطة البكسلات كنص ملون"""
    
    print("\n🖼️  عرض الصورة كنص:")
    
    metadata = pixel_map["metadata"]
    width = metadata["width"]
    height = metadata["height"]
    
    # إنشاء مصفوفة فارغة
    display_matrix = [['?' for _ in range(width)] for _ in range(height)]
    
    # ملء المصفوفة من بيانات البكسلات
    for pixel in pixel_map["pixels"]:
        x, y = pixel["x"], pixel["y"]
        color = pixel["color"]
        
        # تحويل hex إلى رمز
        color_symbols = {
            '#ff0000': '🟥',  # أحمر
            '#00ff00': '🟩',  # أخضر
            '#0000ff': '🟦',  # أزرق
            '#ffffff': '⬜'   # أبيض
        }
        
        symbol = color_symbols.get(color, '⬛')
        display_matrix[y][x] = symbol
    
    # طباعة المصفوفة
    print("   ", end="")
    for x in range(width):
        print(f" {x} ", end="")
    print()
    
    for y in range(height):
        print(f" {y} ", end="")
        for x in range(width):
            print(f" {display_matrix[y][x]} ", end="")
        print()

def analyze_pixel_map(pixel_map):
    """تحليل خريطة البكسلات وعرض الإحصائيات"""
    
    print("\n📊 تحليل خريطة البكسلات:")
    
    metadata = pixel_map["metadata"]
    pixels = pixel_map["pixels"]
    
    print(f"   الأبعاد: {metadata['width']}x{metadata['height']}")
    print(f"   إجمالي البكسلات: {metadata['total_pixels']}")
    print(f"   البكسلات المحفوظة: {len(pixels)}")
    
    # عد الألوان
    color_count = {}
    for pixel in pixels:
        color = pixel["color"]
        color_count[color] = color_count.get(color, 0) + 1
    
    print(f"   الألوان الفريدة: {len(color_count)}")
    print("   توزيع الألوان:")
    
    color_names = {
        '#ff0000': 'أحمر',
        '#00ff00': 'أخضر', 
        '#0000ff': 'أزرق',
        '#ffffff': 'أبيض'
    }
    
    for color, count in color_count.items():
        name = color_names.get(color, 'غير معروف')
        print(f"     {color} ({name}): {count} بكسل")

def demonstrate_compression():
    """عرض توضيحي لمفهوم الضغط"""
    
    print("\n🗜️  مفهوم الضغط:")
    
    # مثال على صورة بها تكرار
    repeated_pattern = {
        "metadata": {
            "width": 4,
            "height": 2,
            "total_pixels": 8,
            "description": "نمط متكرر للضغط"
        },
        "pixels": [
            {"x": 0, "y": 0, "color": "#ff0000"},
            {"x": 1, "y": 0, "color": "#ff0000"},
            {"x": 2, "y": 0, "color": "#00ff00"},
            {"x": 3, "y": 0, "color": "#00ff00"},
            {"x": 0, "y": 1, "color": "#ff0000"},
            {"x": 1, "y": 1, "color": "#ff0000"},
            {"x": 2, "y": 1, "color": "#00ff00"},
            {"x": 3, "y": 1, "color": "#00ff00"}
        ]
    }
    
    # نسخة مضغوطة (مفهومياً)
    compressed_pattern = {
        "metadata": {
            "width": 4,
            "height": 2,
            "compressed": True
        },
        "regions": [
            {"x": 0, "y": 0, "width": 2, "height": 2, "color": "#ff0000"},
            {"x": 2, "y": 0, "width": 2, "height": 2, "color": "#00ff00"}
        ]
    }
    
    original_size = len(json.dumps(repeated_pattern))
    compressed_size = len(json.dumps(compressed_pattern))
    
    print(f"   الحجم الأصلي: {original_size} حرف")
    print(f"   الحجم المضغوط: {compressed_size} حرف")
    print(f"   نسبة الضغط: {((original_size - compressed_size) / original_size * 100):.1f}%")

def main():
    """الدالة الرئيسية للعرض التوضيحي"""
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                    🎨 عرض توضيحي بسيط 🎨                    ║")
    print("║                  مفهوم خريطة البكسلات                       ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    
    # 1. إنشاء خريطة بكسلات بسيطة
    pixel_map = create_simple_pixel_map()
    
    # 2. حفظ الخريطة
    filepath = save_pixel_map(pixel_map, "simple_demo_3x3.json")
    
    # 3. عرض الخريطة
    display_pixel_map_as_text(pixel_map)
    
    # 4. تحليل الخريطة
    analyze_pixel_map(pixel_map)
    
    # 5. عرض مفهوم الضغط
    demonstrate_compression()
    
    # 6. عرض محتوى ملف JSON
    print(f"\n📄 محتوى ملف JSON:")
    print("─" * 50)
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
        print(content)
    
    print("\n" + "─" * 50)
    print("🎉 انتهى العرض التوضيحي!")
    print("هذا مثال مبسط لفهم المفهوم الأساسي.")
    print("التطبيق الكامل يدعم صور حقيقية بملايين البكسلات!")

if __name__ == "__main__":
    main()
