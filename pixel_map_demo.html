<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Pixel Map Converter - عرض توضيحي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .section h2 {
            color: #2196F3;
            margin-bottom: 20px;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn-primary { background: linear-gradient(135deg, #2196F3, #1976D2); }
        .btn-success { background: linear-gradient(135deg, #4CAF50, #388E3C); }
        .btn-warning { background: linear-gradient(135deg, #FF9800, #F57C00); }
        .btn-danger { background: linear-gradient(135deg, #F44336, #D32F2F); }

        .file-input {
            display: none;
        }

        .file-label {
            display: inline-block;
            background: linear-gradient(135deg, #9C27B0, #7B1FA2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .file-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .canvas-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .canvas-box {
            flex: 1;
            min-width: 300px;
            text-align: center;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            max-width: 100%;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .info-box {
            background: #e3f2fd;
            border: 2px solid #2196F3;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .pixel-map-display {
            background: #f5f5f5;
            border: 2px solid #ccc;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2196F3;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .demo-section {
            background: linear-gradient(135deg, #FFF3E0, #FFE0B2);
            border: 2px solid #FF9800;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.3s ease;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .processing {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Pixel Map Converter</h1>
            <p>محول خريطة البكسلات - عرض توضيحي تفاعلي</p>
        </div>

        <div class="main-content">
            <!-- قسم تحويل الصورة إلى خريطة -->
            <div class="section">
                <h2>📷 تحويل صورة إلى خريطة بكسلات</h2>
                
                <input type="file" id="imageInput" class="file-input" accept="image/*">
                <label for="imageInput" class="file-label">📁 اختيار صورة</label>
                
                <button class="btn btn-warning" onclick="convertToPixelMap()" id="convertBtn" disabled>
                    🔄 تحويل إلى خريطة
                </button>
                
                <button class="btn btn-success" onclick="downloadPixelMap()" id="downloadMapBtn" disabled>
                    💾 تحميل الخريطة
                </button>

                <div class="canvas-container">
                    <div class="canvas-box">
                        <h3>الصورة الأصلية</h3>
                        <canvas id="originalCanvas" width="300" height="300"></canvas>
                    </div>
                    <div class="canvas-box">
                        <h3>معاينة الخريطة</h3>
                        <canvas id="previewCanvas" width="300" height="300"></canvas>
                    </div>
                </div>

                <div class="info-box" id="imageInfo">اختر صورة لبدء التحويل...</div>
            </div>

            <!-- قسم إعادة بناء الصورة -->
            <div class="section">
                <h2>🗺️ تحويل خريطة إلى صورة</h2>
                
                <input type="file" id="mapInput" class="file-input" accept=".json">
                <label for="mapInput" class="file-label">📁 اختيار خريطة JSON</label>
                
                <button class="btn btn-danger" onclick="convertToImage()" id="rebuildBtn" disabled>
                    🖼️ إعادة بناء الصورة
                </button>
                
                <button class="btn btn-success" onclick="downloadImage()" id="downloadImgBtn" disabled>
                    💾 تحميل الصورة
                </button>

                <div class="canvas-container">
                    <div class="canvas-box">
                        <h3>الصورة المُعادة</h3>
                        <canvas id="rebuiltCanvas" width="300" height="300"></canvas>
                    </div>
                </div>

                <div class="info-box" id="mapInfo">اختر ملف خريطة JSON لإعادة البناء...</div>
            </div>

            <!-- قسم الإحصائيات -->
            <div class="section">
                <h2>📊 الإحصائيات والمعلومات</h2>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="pixelCount">0</div>
                        <div class="stat-label">عدد البكسلات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="colorCount">0</div>
                        <div class="stat-label">الألوان الفريدة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="compressionRatio">0%</div>
                        <div class="stat-label">نسبة الضغط</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="imageSize">0x0</div>
                        <div class="stat-label">أبعاد الصورة</div>
                    </div>
                </div>

                <div class="pixel-map-display" id="pixelMapDisplay">
                    ستظهر هنا بيانات خريطة البكسلات بتنسيق JSON...
                </div>
            </div>

            <!-- قسم العرض التوضيحي -->
            <div class="section demo-section">
                <h2>🎯 عرض توضيحي سريع</h2>
                <p>جرب هذه الأمثلة السريعة لفهم كيفية عمل التطبيق:</p>
                
                <button class="btn btn-primary" onclick="createDemoImage()">
                    🎨 إنشاء صورة تجريبية
                </button>
                
                <button class="btn btn-warning" onclick="showPixelMapExample()">
                    📄 عرض مثال خريطة بكسلات
                </button>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPixelMap = null;
        let currentImageData = null;

        // متغيرات العناصر
        const imageInput = document.getElementById('imageInput');
        const mapInput = document.getElementById('mapInput');
        const originalCanvas = document.getElementById('originalCanvas');
        const previewCanvas = document.getElementById('previewCanvas');
        const rebuiltCanvas = document.getElementById('rebuiltCanvas');
        const imageInfo = document.getElementById('imageInfo');
        const mapInfo = document.getElementById('mapInfo');
        const pixelMapDisplay = document.getElementById('pixelMapDisplay');

        // إعداد مستمعي الأحداث
        imageInput.addEventListener('change', handleImageSelect);
        mapInput.addEventListener('change', handleMapSelect);

        function handleImageSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    displayOriginalImage(img);
                    document.getElementById('convertBtn').disabled = false;
                    
                    imageInfo.textContent = `✅ تم تحميل الصورة: ${file.name}
الأبعاد: ${img.width}x${img.height}
الحجم: ${(file.size / 1024).toFixed(2)} KB`;
                    
                    updateStats(img.width * img.height, 0, 0, `${img.width}x${img.height}`);
                };
                img.src = e.target.result;
                currentImageData = img;
            };
            reader.readAsDataURL(file);
        }

        function handleMapSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const mapData = JSON.parse(e.target.result);
                    if (mapData.pixels && mapData.metadata) {
                        currentPixelMap = mapData;
                        document.getElementById('rebuildBtn').disabled = false;
                        
                        mapInfo.textContent = `✅ تم تحميل الخريطة: ${file.name}
الأبعاد: ${mapData.metadata.width}x${mapData.metadata.height}
البكسلات: ${mapData.pixels.length}
الألوان الفريدة: ${mapData.metadata.unique_colors || 'غير محدد'}`;
                        
                        displayPixelMap(mapData);
                    } else {
                        mapInfo.textContent = '❌ تنسيق خريطة غير صحيح';
                    }
                } catch (error) {
                    mapInfo.textContent = '❌ خطأ في قراءة ملف JSON';
                }
            };
            reader.readAsText(file);
        }

        function displayOriginalImage(img) {
            const ctx = originalCanvas.getContext('2d');
            const canvas = originalCanvas;
            
            // تغيير حجم الصورة للعرض
            const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
            const newWidth = img.width * scale;
            const newHeight = img.height * scale;
            const x = (canvas.width - newWidth) / 2;
            const y = (canvas.height - newHeight) / 2;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, x, y, newWidth, newHeight);
        }

        function convertToPixelMap() {
            if (!currentImageData) return;

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = currentImageData.width;
            canvas.height = currentImageData.height;
            ctx.drawImage(currentImageData, 0, 0);

            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const pixels = imageData.data;
            
            const pixelMap = {
                metadata: {
                    width: canvas.width,
                    height: canvas.height,
                    total_pixels: canvas.width * canvas.height,
                    version: "1.0",
                    created: new Date().toISOString()
                },
                pixels: []
            };

            const colorSet = new Set();
            
            // تحويل البكسلات
            for (let y = 0; y < canvas.height; y++) {
                for (let x = 0; x < canvas.width; x++) {
                    const index = (y * canvas.width + x) * 4;
                    const r = pixels[index];
                    const g = pixels[index + 1];
                    const b = pixels[index + 2];
                    const a = pixels[index + 3];
                    
                    if (a > 0) { // تجاهل البكسلات الشفافة
                        const hexColor = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
                        colorSet.add(hexColor);
                        
                        pixelMap.pixels.push({
                            x: x,
                            y: y,
                            color: hexColor
                        });
                    }
                }
            }

            pixelMap.metadata.unique_colors = colorSet.size;
            currentPixelMap = pixelMap;

            // عرض معاينة
            displayPixelMapPreview(pixelMap);
            displayPixelMap(pixelMap);
            
            // تحديث الإحصائيات
            updateStats(
                pixelMap.pixels.length,
                colorSet.size,
                calculateCompressionRatio(pixelMap),
                `${canvas.width}x${canvas.height}`
            );

            document.getElementById('downloadMapBtn').disabled = false;
            
            imageInfo.textContent += `

✅ تم التحويل بنجاح!
البكسلات المعالجة: ${pixelMap.pixels.length}
الألوان الفريدة: ${colorSet.size}`;
        }

        function displayPixelMapPreview(pixelMap) {
            const ctx = previewCanvas.getContext('2d');
            const canvas = previewCanvas;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const scaleX = canvas.width / pixelMap.metadata.width;
            const scaleY = canvas.height / pixelMap.metadata.height;
            
            pixelMap.pixels.forEach(pixel => {
                ctx.fillStyle = pixel.color;
                ctx.fillRect(
                    pixel.x * scaleX,
                    pixel.y * scaleY,
                    scaleX,
                    scaleY
                );
            });
        }

        function convertToImage() {
            if (!currentPixelMap) return;

            const canvas = rebuiltCanvas;
            const ctx = canvas.getContext('2d');
            const metadata = currentPixelMap.metadata;
            
            // تنظيف الكانفاس
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            const scaleX = canvas.width / metadata.width;
            const scaleY = canvas.height / metadata.height;
            
            currentPixelMap.pixels.forEach(pixel => {
                ctx.fillStyle = pixel.color;
                ctx.fillRect(
                    pixel.x * scaleX,
                    pixel.y * scaleY,
                    scaleX,
                    scaleY
                );
            });

            document.getElementById('downloadImgBtn').disabled = false;
            
            mapInfo.textContent += `

✅ تم إعادة بناء الصورة بنجاح!`;
        }

        function displayPixelMap(pixelMap) {
            const display = pixelMapDisplay;
            const preview = {
                metadata: pixelMap.metadata,
                pixels: pixelMap.pixels.slice(0, 10) // عرض أول 10 بكسلات فقط
            };
            
            if (pixelMap.pixels.length > 10) {
                preview.pixels.push({
                    note: `... و ${pixelMap.pixels.length - 10} بكسل إضافي`
                });
            }
            
            display.textContent = JSON.stringify(preview, null, 2);
        }

        function updateStats(pixelCount, colorCount, compressionRatio, imageSize) {
            document.getElementById('pixelCount').textContent = pixelCount.toLocaleString();
            document.getElementById('colorCount').textContent = colorCount.toLocaleString();
            document.getElementById('compressionRatio').textContent = compressionRatio + '%';
            document.getElementById('imageSize').textContent = imageSize;
        }

        function calculateCompressionRatio(pixelMap) {
            const originalSize = pixelMap.metadata.total_pixels * 3; // RGB
            const mapSize = JSON.stringify(pixelMap).length;
            return Math.round((1 - mapSize / originalSize) * 100);
        }

        function downloadPixelMap() {
            if (!currentPixelMap) return;
            
            const dataStr = JSON.stringify(currentPixelMap, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = 'pixel_map.json';
            link.click();
            
            URL.revokeObjectURL(url);
        }

        function downloadImage() {
            const canvas = rebuiltCanvas;
            const link = document.createElement('a');
            link.href = canvas.toDataURL('image/png');
            link.download = 'restored_image.png';
            link.click();
        }

        function createDemoImage() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 50;
            canvas.height = 50;
            
            // رسم نمط ملون
            const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF'];
            
            for (let y = 0; y < 50; y++) {
                for (let x = 0; x < 50; x++) {
                    const colorIndex = (x + y) % colors.length;
                    ctx.fillStyle = colors[colorIndex];
                    ctx.fillRect(x, y, 1, 1);
                }
            }
            
            // تحويل إلى صورة وعرضها
            const img = new Image();
            img.onload = function() {
                currentImageData = img;
                displayOriginalImage(img);
                document.getElementById('convertBtn').disabled = false;
                
                imageInfo.textContent = `✅ تم إنشاء صورة تجريبية
الأبعاد: 50x50
النمط: شطرنج ملون`;
                
                updateStats(2500, 5, 0, '50x50');
            };
            img.src = canvas.toDataURL();
        }

        function showPixelMapExample() {
            const exampleMap = {
                metadata: {
                    width: 3,
                    height: 3,
                    total_pixels: 9,
                    unique_colors: 3,
                    version: "1.0"
                },
                pixels: [
                    {x: 0, y: 0, color: "#FF0000"},
                    {x: 1, y: 0, color: "#00FF00"},
                    {x: 2, y: 0, color: "#0000FF"},
                    {x: 0, y: 1, color: "#FFFF00"},
                    {x: 1, y: 1, color: "#FF00FF"},
                    {x: 2, y: 1, color: "#00FFFF"},
                    {x: 0, y: 2, color: "#800000"},
                    {x: 1, y: 2, color: "#008000"},
                    {x: 2, y: 2, color: "#000080"}
                ]
            };
            
            currentPixelMap = exampleMap;
            displayPixelMap(exampleMap);
            document.getElementById('rebuildBtn').disabled = false;
            
            mapInfo.textContent = `✅ مثال خريطة بكسلات 3x3
البكسلات: 9
الألوان الفريدة: 9`;
        }

        // رسالة ترحيب
        window.onload = function() {
            imageInfo.textContent = `🎨 مرحباً بك في Pixel Map Converter!

هذا عرض توضيحي تفاعلي يعمل في المتصفح مباشرة.

يمكنك:
1. اختيار صورة وتحويلها إلى خريطة بكسلات
2. تحميل خريطة JSON وإعادة بناء الصورة
3. تجربة الأمثلة السريعة

ابدأ باختيار صورة أو جرب العرض التوضيحي!`;
        };
    </script>
</body>
</html>
