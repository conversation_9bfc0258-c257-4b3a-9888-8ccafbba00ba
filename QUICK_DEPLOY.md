# ⚡ النشر السريع - Image Encryption Website

## 🚀 أوامر النشر السريع

### **1. تشغيل محلي سريع:**
```bash
# تشغيل خادم محلي
npx http-server public -p 8000

# فتح المتصفح على:
# http://localhost:8000
```

### **2. نشر Firebase سريع:**
```bash
# تسجيل دخول (مرة واحدة فقط)
firebase login

# نشر فوري
firebase deploy

# أو نشر الموقع فقط
firebase deploy --only hosting
```

### **3. أوامر مفيدة:**
```bash
# عرض الموقع المنشور
firebase open hosting:site

# عرض المشاريع
firebase projects:list

# تبديل المشروع
firebase use mage-41f51
```

## 🔧 إعداد سريع لأول مرة

### **إذا لم يكن Firebase مُعد:**
```bash
# 1. تثبيت Firebase CLI
npm install -g firebase-tools

# 2. تسجيل الدخول
firebase login

# 3. ربط المشروع
firebase use mage-41f51

# 4. النشر
firebase deploy
```

## 📱 اختبار سريع

### **قائمة التحقق:**
- [ ] الموقع يفتح بدون أخطاء
- [ ] يمكن إنشاء مفتاح خاص
- [ ] يمكن تشفير صورة
- [ ] يمكن فك تشفير خريطة
- [ ] يعمل على الجوال

### **روابط الاختبار:**
- **محلي**: http://localhost:8000
- **Firebase**: https://mage-41f51.web.app
- **الدومين**: https://imageencryption.com

## 🎯 نشر الإنتاج

### **خطوات النشر النهائي:**
```bash
# 1. اختبار محلي
npx http-server public -p 8000

# 2. نشر قواعد البيانات
firebase deploy --only firestore:rules
firebase deploy --only storage

# 3. نشر الموقع
firebase deploy --only hosting

# 4. التحقق من النشر
curl -I https://imageencryption.com
```

## 🔍 استكشاف الأخطاء السريع

### **مشاكل شائعة:**

**1. خطأ في Firebase CLI:**
```bash
# إعادة تثبيت
npm uninstall -g firebase-tools
npm install -g firebase-tools
```

**2. خطأ في الصلاحيات:**
```bash
# إعادة تسجيل الدخول
firebase logout
firebase login
```

**3. خطأ في المشروع:**
```bash
# التحقق من المشروع النشط
firebase use
# تبديل المشروع
firebase use mage-41f51
```

## 📊 مراقبة سريعة

### **فحص الحالة:**
```bash
# حالة الاستضافة
firebase hosting:channel:list

# سجلات الأخطاء
firebase functions:log --limit 10

# إحصائيات الاستخدام
# زيارة: https://console.firebase.google.com/project/mage-41f51
```

---

## ⚡ أوامر النشر بنقرة واحدة

### **Windows (PowerShell):**
```powershell
# حفظ كملف deploy.ps1
firebase deploy --only hosting
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم النشر بنجاح!" -ForegroundColor Green
    Start-Process "https://imageencryption.com"
} else {
    Write-Host "❌ فشل النشر!" -ForegroundColor Red
}
```

### **Linux/Mac (Bash):**
```bash
#!/bin/bash
# حفظ كملف deploy.sh
firebase deploy --only hosting
if [ $? -eq 0 ]; then
    echo "✅ تم النشر بنجاح!"
    open https://imageencryption.com
else
    echo "❌ فشل النشر!"
fi
```

### **تشغيل النشر:**
```bash
# Windows
./deploy.ps1

# Linux/Mac
chmod +x deploy.sh
./deploy.sh
```

---

**🎉 الموقع جاهز على: https://imageencryption.com 🚀**

**📱 اختبر الآن: http://localhost:8000**
