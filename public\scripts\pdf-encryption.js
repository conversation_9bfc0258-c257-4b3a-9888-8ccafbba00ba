// PDF Encryption Module
console.log('📄 تم تحميل pdf-encryption.js بنجاح');

// متغيرات PDF
let currentPdfFile = null;
let encryptedPdfData = null;
let decryptedPdfData = null;

// تبديل تبويبات PDF
function switchPdfTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.pdf-tabs .tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إظهار التبويب المحدد
    if (tabName === 'encrypt') {
        document.getElementById('pdf-encrypt-tab').classList.add('active');
        document.querySelector('.pdf-tabs .tab-btn:first-child').classList.add('active');
    } else if (tabName === 'decrypt') {
        document.getElementById('pdf-decrypt-tab').classList.add('active');
        document.querySelector('.pdf-tabs .tab-btn:last-child').classList.add('active');
    }
}

// معالج رفع ملف PDF
document.addEventListener('DOMContentLoaded', function() {
    const pdfInput = document.getElementById('pdfInput');
    const encryptedPdfInput = document.getElementById('encryptedPdfInput');
    
    if (pdfInput) {
        pdfInput.addEventListener('change', handlePdfUpload);
    }
    
    if (encryptedPdfInput) {
        encryptedPdfInput.addEventListener('change', handleEncryptedPdfUpload);
    }
});

// معالجة رفع ملف PDF للتشفير
function handlePdfUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (file.type !== 'application/pdf') {
        showNotification('❌ يرجى اختيار ملف PDF صالح', 'error');
        return;
    }
    
    currentPdfFile = file;
    
    // تحديث معلومات الملف
    const info = document.getElementById('pdfEncryptionInfo');
    info.innerHTML = `
        <div class="file-selected">
            <h4>📄 ${file.name}</h4>
            <p>الحجم: ${formatFileSize(file.size)}</p>
            <p>النوع: PDF</p>
            <p class="success">✅ جاهز للتشفير</p>
        </div>
    `;
    
    // تفعيل زر التشفير
    const encryptBtn = document.getElementById('pdfEncryptBtn');
    if (encryptBtn && getCurrentKey()) {
        encryptBtn.disabled = false;
    }
    
    console.log('📄 تم تحميل ملف PDF:', file.name);
}

// معالجة رفع ملف PDF المشفر
function handleEncryptedPdfUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (file.type !== 'application/json') {
        showNotification('❌ يرجى اختيار ملف JSON صالح', 'error');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);
            
            // التحقق من صحة البيانات
            if (!data.encryptedData || !data.metadata || data.metadata.type !== 'pdf') {
                throw new Error('ملف غير صالح');
            }
            
            encryptedPdfData = data;
            
            // تحديث معلومات الملف
            const info = document.getElementById('pdfDecryptionInfo');
            info.innerHTML = `
                <div class="file-selected">
                    <h4>📄 ${data.metadata.originalName || 'ملف PDF مشفر'}</h4>
                    <p>الحجم الأصلي: ${formatFileSize(data.metadata.originalSize || 0)}</p>
                    <p>تاريخ التشفير: ${new Date(data.metadata.timestamp).toLocaleDateString('ar-SA')}</p>
                    <p class="success">✅ جاهز لفك التشفير</p>
                </div>
            `;
            
            // تفعيل زر فك التشفير
            const decryptBtn = document.getElementById('pdfDecryptBtn');
            if (decryptBtn && getCurrentKey()) {
                decryptBtn.disabled = false;
            }
            
            console.log('📄 تم تحميل ملف PDF مشفر');
            
        } catch (error) {
            showNotification('❌ ملف JSON غير صالح أو تالف', 'error');
            console.error('خطأ في قراءة الملف المشفر:', error);
        }
    };
    
    reader.readAsText(file);
}

// تشفير ملف PDF
async function encryptPDF() {
    if (!currentPdfFile) {
        showNotification('❌ يرجى اختيار ملف PDF أولاً', 'error');
        return;
    }
    
    const currentKey = getCurrentKey();
    if (!currentKey) {
        showNotification('❌ يرجى إنشاء مفتاح تشفير أولاً', 'error');
        return;
    }
    
    try {
        showNotification('🔄 جاري تشفير ملف PDF...', 'info');
        
        // قراءة ملف PDF كـ ArrayBuffer
        const arrayBuffer = await readFileAsArrayBuffer(currentPdfFile);
        const uint8Array = new Uint8Array(arrayBuffer);
        
        // تحويل إلى Base64
        const base64Data = arrayBufferToBase64(arrayBuffer);
        
        // الحصول على مستوى التشفير
        const encryptionLevel = document.getElementById('pdfEncryptionLevel').value;
        
        // تشفير البيانات
        const encryptedData = await encryptData(base64Data, currentKey, encryptionLevel);
        
        // إنشاء metadata
        const metadata = {
            type: 'pdf',
            originalName: currentPdfFile.name,
            originalSize: currentPdfFile.size,
            encryptionLevel: encryptionLevel,
            timestamp: new Date().toISOString(),
            version: '1.0',
            source: 'local-encryption'
        };
        
        // إنشاء الملف المشفر
        const encryptedFile = {
            encryptedData: encryptedData,
            metadata: metadata
        };
        
        encryptedPdfData = encryptedFile;
        
        // تفعيل زر التحميل
        const downloadBtn = document.getElementById('downloadPdfBtn');
        if (downloadBtn) {
            downloadBtn.disabled = false;
        }
        
        // تحديث المعلومات
        const info = document.getElementById('pdfEncryptionInfo');
        info.innerHTML = `
            <div class="encryption-success">
                <h4>✅ تم تشفير PDF بنجاح!</h4>
                <p>الملف الأصلي: ${currentPdfFile.name}</p>
                <p>الحجم الأصلي: ${formatFileSize(currentPdfFile.size)}</p>
                <p>مستوى التشفير: ${getEncryptionLevelName(encryptionLevel)}</p>
                <p class="success">🔐 جاهز للتحميل</p>
            </div>
        `;
        
        showNotification('✅ تم تشفير ملف PDF بنجاح!', 'success');
        console.log('📄 تم تشفير PDF بنجاح');
        
    } catch (error) {
        showNotification('❌ فشل في تشفير ملف PDF', 'error');
        console.error('خطأ في تشفير PDF:', error);
    }
}

// فك تشفير ملف PDF
async function decryptPDF() {
    if (!encryptedPdfData) {
        showNotification('❌ يرجى اختيار ملف PDF مشفر أولاً', 'error');
        return;
    }
    
    const currentKey = getCurrentKey();
    if (!currentKey) {
        showNotification('❌ يرجى إدخال مفتاح فك التشفير', 'error');
        return;
    }
    
    try {
        showNotification('🔄 جاري فك تشفير ملف PDF...', 'info');
        
        // فك تشفير البيانات
        const decryptedBase64 = await decryptData(
            encryptedPdfData.encryptedData, 
            currentKey, 
            encryptedPdfData.metadata.encryptionLevel
        );
        
        // تحويل من Base64 إلى ArrayBuffer
        const arrayBuffer = base64ToArrayBuffer(decryptedBase64);
        
        decryptedPdfData = {
            data: arrayBuffer,
            name: encryptedPdfData.metadata.originalName || 'decrypted.pdf',
            size: encryptedPdfData.metadata.originalSize || arrayBuffer.byteLength
        };
        
        // تفعيل زر التحميل
        const downloadBtn = document.getElementById('downloadDecryptedPdfBtn');
        if (downloadBtn) {
            downloadBtn.disabled = false;
        }
        
        // تحديث المعلومات
        const info = document.getElementById('pdfDecryptionInfo');
        info.innerHTML = `
            <div class="decryption-success">
                <h4>✅ تم فك تشفير PDF بنجاح!</h4>
                <p>اسم الملف: ${decryptedPdfData.name}</p>
                <p>الحجم: ${formatFileSize(decryptedPdfData.size)}</p>
                <p>تاريخ التشفير: ${new Date(encryptedPdfData.metadata.timestamp).toLocaleDateString('ar-SA')}</p>
                <p class="success">📄 جاهز للتحميل</p>
            </div>
        `;
        
        showNotification('✅ تم فك تشفير ملف PDF بنجاح!', 'success');
        console.log('📄 تم فك تشفير PDF بنجاح');
        
    } catch (error) {
        showNotification('❌ فشل في فك تشفير ملف PDF - تأكد من صحة المفتاح', 'error');
        console.error('خطأ في فك تشفير PDF:', error);
    }
}

// تحميل ملف PDF المشفر
function downloadEncryptedPDF() {
    if (!encryptedPdfData) {
        showNotification('❌ لا يوجد ملف مشفر للتحميل', 'error');
        return;
    }
    
    try {
        const jsonString = JSON.stringify(encryptedPdfData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        
        const originalName = currentPdfFile ? currentPdfFile.name : 'document.pdf';
        const fileName = originalName.replace('.pdf', '_encrypted.json');
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showNotification('📥 تم تحميل ملف PDF المشفر', 'success');
        console.log('📥 تم تحميل PDF مشفر:', fileName);
        
    } catch (error) {
        showNotification('❌ فشل في تحميل الملف المشفر', 'error');
        console.error('خطأ في تحميل PDF مشفر:', error);
    }
}

// تحميل ملف PDF المفكوك التشفير
function downloadDecryptedPDF() {
    if (!decryptedPdfData) {
        showNotification('❌ لا يوجد ملف مفكوك التشفير للتحميل', 'error');
        return;
    }
    
    try {
        const blob = new Blob([decryptedPdfData.data], { type: 'application/pdf' });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = decryptedPdfData.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showNotification('📥 تم تحميل ملف PDF الأصلي', 'success');
        console.log('📥 تم تحميل PDF مفكوك التشفير:', decryptedPdfData.name);
        
    } catch (error) {
        showNotification('❌ فشل في تحميل الملف', 'error');
        console.error('خطأ في تحميل PDF مفكوك التشفير:', error);
    }
}

// وظائف مساعدة
function readFileAsArrayBuffer(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsArrayBuffer(file);
    });
}

function arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}

function base64ToArrayBuffer(base64) {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
        bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
}

function getEncryptionLevelName(level) {
    switch (level) {
        case 'standard': return '🔒 قياسي (سريع)';
        case 'high': return '🛡️ عالي (موصى به)';
        case 'maximum': return '🔐 أقصى (أبطأ)';
        default: return level;
    }
}

// تحديث حالة أزرار PDF عند تغيير المفتاح
function updatePdfButtonStates() {
    const currentKey = getCurrentKey();
    
    // زر تشفير PDF
    const encryptBtn = document.getElementById('pdfEncryptBtn');
    if (encryptBtn) {
        encryptBtn.disabled = !currentKey || !currentPdfFile;
    }
    
    // زر فك تشفير PDF
    const decryptBtn = document.getElementById('pdfDecryptBtn');
    if (decryptBtn) {
        decryptBtn.disabled = !currentKey || !encryptedPdfData;
    }
}

// استدعاء تحديث حالة الأزرار عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث حالة الأزرار كل ثانية
    setInterval(updatePdfButtonStates, 1000);
});

console.log('📄 تم تهيئة وحدة تشفير PDF بنجاح');
