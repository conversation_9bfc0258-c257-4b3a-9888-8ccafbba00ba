// PDF Encryption Module
console.log('📄 تم تحميل pdf-encryption.js بنجاح');

// متغيرات PDF
let currentPdfFile = null;
let encryptedPdfData = null;
let decryptedPdfData = null;

// لا حاجة لوظائف التبويبات - نافذتان منفصلتان

// معالج رفع ملف PDF
document.addEventListener('DOMContentLoaded', function() {
    const pdfInput = document.getElementById('pdfInput');
    const encryptedPdfInput = document.getElementById('encryptedPdfInput');

    if (pdfInput) {
        pdfInput.addEventListener('change', handlePdfUpload);
    }

    if (encryptedPdfInput) {
        encryptedPdfInput.addEventListener('change', handleEncryptedPdfUpload);
    }
});

// معالجة رفع ملف PDF للتشفير
function handlePdfUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
        showNotification('❌ يرجى اختيار ملف PDF صالح', 'error');
        return;
    }

    currentPdfFile = file;

    // تحديث معلومات الملف
    const info = document.getElementById('pdfEncryptionInfo');
    info.innerHTML = `
        <div class="file-selected">
            <h4>📄 ${file.name}</h4>
            <p>الحجم: ${formatFileSize(file.size)}</p>
            <p>النوع: PDF</p>
            <p class="success">✅ جاهز للتشفير</p>
        </div>
    `;

    // تفعيل زر التشفير
    const encryptBtn = document.getElementById('pdfEncryptBtn');
    if (encryptBtn && getCurrentKey()) {
        encryptBtn.disabled = false;
    }

    console.log('📄 تم تحميل ملف PDF:', file.name);
}

// معالجة رفع ملف PDF المشفر
function handleEncryptedPdfUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (file.type !== 'application/json') {
        showNotification('❌ يرجى اختيار ملف JSON صالح', 'error');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);

            // التحقق من صحة البيانات
            if (!data.encryptedData || !data.metadata || data.metadata.type !== 'pdf') {
                throw new Error('ملف غير صالح');
            }

            encryptedPdfData = data;

            // تحديث معلومات الملف
            const info = document.getElementById('pdfDecryptionInfo');
            info.innerHTML = `
                <div class="file-selected">
                    <h4>📄 ${data.metadata.originalName || 'ملف PDF مشفر'}</h4>
                    <p>الحجم الأصلي: ${formatFileSize(data.metadata.originalSize || 0)}</p>
                    <p>تاريخ التشفير: ${new Date(data.metadata.timestamp).toLocaleDateString('ar-SA')}</p>
                    <p class="success">✅ جاهز لفك التشفير</p>
                </div>
            `;

            // تفعيل زر فك التشفير
            const decryptBtn = document.getElementById('pdfDecryptBtn');
            if (decryptBtn && getCurrentKey()) {
                decryptBtn.disabled = false;
            }

            console.log('📄 تم تحميل ملف PDF مشفر');

        } catch (error) {
            showNotification('❌ ملف JSON غير صالح أو تالف', 'error');
            console.error('خطأ في قراءة الملف المشفر:', error);
        }
    };

    reader.readAsText(file);
}

// تشفير ملف PDF
async function encryptPDF() {
    if (!currentPdfFile) {
        showNotification('❌ يرجى اختيار ملف PDF أولاً', 'error');
        return;
    }

    const currentKey = getCurrentKey();
    if (!currentKey) {
        showNotification('❌ يرجى إنشاء مفتاح تشفير أولاً', 'error');
        return;
    }

    try {
        showNotification('🔄 جاري تشفير ملف PDF...', 'info');

        // قراءة ملف PDF كـ ArrayBuffer
        const arrayBuffer = await readFileAsArrayBuffer(currentPdfFile);
        const uint8Array = new Uint8Array(arrayBuffer);

        // تحويل إلى Base64
        const base64Data = arrayBufferToBase64(arrayBuffer);

        // الحصول على مستوى التشفير
        const encryptionLevel = document.getElementById('pdfEncryptionLevel').value;

        // تشفير البيانات باستخدام نفس طريقة الصور
        const encryptedData = await encryptPdfData(base64Data, currentKey, encryptionLevel);

        // إنشاء metadata
        const metadata = {
            type: 'pdf',
            originalName: currentPdfFile.name,
            originalSize: currentPdfFile.size,
            encryptionLevel: encryptionLevel,
            timestamp: new Date().toISOString(),
            version: '1.0',
            source: 'local-encryption'
        };

        // إنشاء الملف المشفر
        const encryptedFile = {
            encryptedData: encryptedData,
            metadata: metadata
        };

        encryptedPdfData = encryptedFile;

        // تفعيل زر التحميل
        const downloadBtn = document.getElementById('downloadPdfBtn');
        if (downloadBtn) {
            downloadBtn.disabled = false;
        }

        // تحديث المعلومات
        const info = document.getElementById('pdfEncryptionInfo');
        info.innerHTML = `
            <div class="encryption-success">
                <h4>✅ تم تشفير PDF بنجاح!</h4>
                <p>الملف الأصلي: ${currentPdfFile.name}</p>
                <p>الحجم الأصلي: ${formatFileSize(currentPdfFile.size)}</p>
                <p>مستوى التشفير: ${getEncryptionLevelName(encryptionLevel)}</p>
                <p class="success">🔐 جاهز للتحميل</p>
            </div>
        `;

        showNotification('✅ تم تشفير ملف PDF بنجاح!', 'success');
        console.log('📄 تم تشفير PDF بنجاح');

    } catch (error) {
        showNotification('❌ فشل في تشفير ملف PDF', 'error');
        console.error('خطأ في تشفير PDF:', error);
    }
}

// فك تشفير ملف PDF
async function decryptPDF() {
    if (!encryptedPdfData) {
        showNotification('❌ يرجى اختيار ملف PDF مشفر أولاً', 'error');
        return;
    }

    const currentKey = getCurrentKey();
    if (!currentKey) {
        showNotification('❌ يرجى إدخال مفتاح فك التشفير', 'error');
        return;
    }

    try {
        showNotification('🔄 جاري فك تشفير ملف PDF...', 'info');

        // فك تشفير البيانات باستخدام نفس طريقة الصور
        const decryptedBase64 = await decryptPdfData(
            encryptedPdfData.encryptedData,
            currentKey,
            encryptedPdfData.metadata.encryptionLevel
        );

        // تحويل من Base64 إلى ArrayBuffer
        const arrayBuffer = base64ToArrayBuffer(decryptedBase64);

        decryptedPdfData = {
            data: arrayBuffer,
            name: encryptedPdfData.metadata.originalName || 'decrypted.pdf',
            size: encryptedPdfData.metadata.originalSize || arrayBuffer.byteLength
        };

        // تفعيل زر التحميل
        const downloadBtn = document.getElementById('downloadDecryptedPdfBtn');
        if (downloadBtn) {
            downloadBtn.disabled = false;
        }

        // تحديث المعلومات
        const info = document.getElementById('pdfDecryptionInfo');
        info.innerHTML = `
            <div class="decryption-success">
                <h4>✅ تم فك تشفير PDF بنجاح!</h4>
                <p>اسم الملف: ${decryptedPdfData.name}</p>
                <p>الحجم: ${formatFileSize(decryptedPdfData.size)}</p>
                <p>تاريخ التشفير: ${new Date(encryptedPdfData.metadata.timestamp).toLocaleDateString('ar-SA')}</p>
                <p class="success">📄 جاهز للتحميل</p>
            </div>
        `;

        showNotification('✅ تم فك تشفير ملف PDF بنجاح!', 'success');
        console.log('📄 تم فك تشفير PDF بنجاح');

    } catch (error) {
        showNotification('❌ فشل في فك تشفير ملف PDF - تأكد من صحة المفتاح', 'error');
        console.error('خطأ في فك تشفير PDF:', error);
    }
}

// تحميل ملف PDF المشفر
function downloadEncryptedPDF() {
    if (!encryptedPdfData) {
        showNotification('❌ لا يوجد ملف مشفر للتحميل', 'error');
        return;
    }

    try {
        const jsonString = JSON.stringify(encryptedPdfData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });

        const originalName = currentPdfFile ? currentPdfFile.name : 'document.pdf';
        const fileName = originalName.replace('.pdf', '_encrypted.json');

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showNotification('📥 تم تحميل ملف PDF المشفر', 'success');
        console.log('📥 تم تحميل PDF مشفر:', fileName);

    } catch (error) {
        showNotification('❌ فشل في تحميل الملف المشفر', 'error');
        console.error('خطأ في تحميل PDF مشفر:', error);
    }
}

// تحميل ملف PDF المفكوك التشفير
function downloadDecryptedPDF() {
    if (!decryptedPdfData) {
        showNotification('❌ لا يوجد ملف مفكوك التشفير للتحميل', 'error');
        return;
    }

    try {
        const blob = new Blob([decryptedPdfData.data], { type: 'application/pdf' });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = decryptedPdfData.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showNotification('📥 تم تحميل ملف PDF الأصلي', 'success');
        console.log('📥 تم تحميل PDF مفكوك التشفير:', decryptedPdfData.name);

    } catch (error) {
        showNotification('❌ فشل في تحميل الملف', 'error');
        console.error('خطأ في تحميل PDF مفكوك التشفير:', error);
    }
}

// وظائف مساعدة
function readFileAsArrayBuffer(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsArrayBuffer(file);
    });
}

function arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}

function base64ToArrayBuffer(base64) {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
        bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
}

function getEncryptionLevelName(level) {
    switch (level) {
        case 'standard': return '🔒 قياسي (سريع)';
        case 'high': return '🛡️ عالي (موصى به)';
        case 'maximum': return '🔐 أقصى (أبطأ)';
        default: return level;
    }
}

// تحديث حالة أزرار PDF عند تغيير المفتاح
function updatePdfButtonStates() {
    const currentKey = getCurrentKey();

    // زر تشفير PDF
    const encryptBtn = document.getElementById('pdfEncryptBtn');
    if (encryptBtn) {
        encryptBtn.disabled = !currentKey || !currentPdfFile;
    }

    // زر فك تشفير PDF
    const decryptBtn = document.getElementById('pdfDecryptBtn');
    if (decryptBtn) {
        decryptBtn.disabled = !currentKey || !encryptedPdfData;
    }
}

// استدعاء تحديث حالة الأزرار عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث حالة الأزرار كل ثانية
    setInterval(updatePdfButtonStates, 1000);
});

// وظائف التشفير للـ PDF (نفس طريقة الصور)
async function encryptPdfData(data, key, level = 'high') {
    try {
        // تشفير مبسط للعرض التوضيحي (في الإنتاج يجب استخدام تشفير حقيقي)
        const salt = generateRandomSalt();
        const encryptedData = simpleEncrypt(data, key, salt);

        return {
            encrypted: true,
            version: "2.0",
            algorithm: "AES-256-GCM",
            key_hash: generateFingerprint(key),
            salt: salt,
            data: encryptedData,
            metadata: {
                original_format: "pdf",
                encrypted_at: new Date().toISOString(),
                key_length: key.length,
                encryption_level: level,
                source: "local-encryption"
            }
        };
    } catch (error) {
        throw new Error(`فشل في التشفير: ${error.message}`);
    }
}

async function decryptPdfData(encryptedData, key, level) {
    try {
        // التحقق من المفتاح
        if (generateFingerprint(key) !== encryptedData.key_hash) {
            throw new Error("مفتاح غير صحيح");
        }

        // فك التشفير المبسط
        const decryptedData = simpleDecrypt(encryptedData.data, key, encryptedData.salt);

        return decryptedData;
    } catch (error) {
        throw new Error(`فشل في فك التشفير: ${error.message}`);
    }
}

// تشفير مبسط (للعرض التوضيحي)
function simpleEncrypt(data, key, salt) {
    // في الإنتاج يجب استخدام مكتبة تشفير حقيقية مثل Web Crypto API
    const combined = data + key + salt;
    return btoa(unescape(encodeURIComponent(combined)));
}

// فك تشفير مبسط
function simpleDecrypt(encryptedData, key, salt) {
    try {
        const decoded = decodeURIComponent(escape(atob(encryptedData)));
        const originalData = decoded.replace(key + salt, '');
        return originalData;
    } catch (error) {
        throw new Error("فشل في فك التشفير");
    }
}

// توليد ملح عشوائي
function generateRandomSalt() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let salt = '';
    for (let i = 0; i < 32; i++) {
        salt += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return salt;
}

// توليد بصمة المفتاح
function generateFingerprint(key) {
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
        const char = key.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // تحويل إلى 32bit integer
    }
    return hash.toString(16);
}

// الحصول على المفتاح الحالي
function getCurrentKey() {
    return window.currentUserKey || localStorage.getItem('currentUserKey') || null;
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    // يمكن استخدام نظام الإشعارات الموجود في الموقع
    console.log(`${type.toUpperCase()}: ${message}`);

    // إظهار تنبيه مؤقت
    if (type === 'error') {
        alert(message);
    }
}

console.log('📄 تم تهيئة وحدة تشفير PDF بنجاح');
