#!/usr/bin/env python3
"""
إنشاء صورة تجريبية للاختبار
"""

from PIL import Image, ImageDraw
import os

def create_test_images():
    """إنشاء صور تجريبية للاختبار"""
    
    # إنشاء مجلد examples إذا لم يكن موجوداً
    os.makedirs("examples", exist_ok=True)
    
    # 1. صورة بسيطة ملونة (10x10)
    print("🎨 إنشاء صورة بسيطة...")
    simple_img = Image.new("RGB", (10, 10), "white")
    pixels = simple_img.load()
    
    # رسم نمط بسيط
    colors = [
        (255, 0, 0),    # أحمر
        (0, 255, 0),    # أخضر
        (0, 0, 255),    # أزرق
        (255, 255, 0),  # أصفر
        (255, 0, 255),  # بنفسجي
    ]
    
    for y in range(10):
        for x in range(10):
            color_index = (x + y) % len(colors)
            pixels[x, y] = colors[color_index]
    
    simple_img.save("examples/simple_10x10.png")
    print("   ✅ تم حفظ: examples/simple_10x10.png")
    
    # 2. صورة متوسطة مع نمط (50x50)
    print("🎨 إنشاء صورة متوسطة...")
    medium_img = Image.new("RGB", (50, 50), "white")
    draw = ImageDraw.Draw(medium_img)
    
    # رسم دوائر ملونة
    draw.ellipse([5, 5, 20, 20], fill=(255, 0, 0))      # دائرة حمراء
    draw.ellipse([25, 5, 40, 20], fill=(0, 255, 0))     # دائرة خضراء
    draw.ellipse([5, 25, 20, 40], fill=(0, 0, 255))     # دائرة زرقاء
    draw.ellipse([25, 25, 40, 40], fill=(255, 255, 0))  # دائرة صفراء
    
    medium_img.save("examples/medium_50x50.png")
    print("   ✅ تم حفظ: examples/medium_50x50.png")
    
    # 3. صورة مع شفافية
    print("🎨 إنشاء صورة مع شفافية...")
    transparent_img = Image.new("RGBA", (30, 30), (255, 255, 255, 0))
    draw = ImageDraw.Draw(transparent_img)
    
    # رسم مربعات بشفافية مختلفة
    draw.rectangle([5, 5, 15, 15], fill=(255, 0, 0, 255))    # أحمر غير شفاف
    draw.rectangle([15, 5, 25, 15], fill=(0, 255, 0, 128))   # أخضر نصف شفاف
    draw.rectangle([5, 15, 15, 25], fill=(0, 0, 255, 64))    # أزرق شفاف
    draw.rectangle([15, 15, 25, 25], fill=(255, 255, 0, 192)) # أصفر شبه شفاف
    
    transparent_img.save("examples/transparent_30x30.png")
    print("   ✅ تم حفظ: examples/transparent_30x30.png")
    
    # 4. صورة نمط شطرنج
    print("🎨 إنشاء نمط شطرنج...")
    chess_img = Image.new("RGB", (40, 40), "white")
    pixels = chess_img.load()
    
    for y in range(40):
        for x in range(40):
            if (x // 5 + y // 5) % 2 == 0:
                pixels[x, y] = (0, 0, 0)      # أسود
            else:
                pixels[x, y] = (255, 255, 255) # أبيض
    
    chess_img.save("examples/chess_40x40.png")
    print("   ✅ تم حفظ: examples/chess_40x40.png")
    
    # 5. صورة تدرج لوني
    print("🎨 إنشاء تدرج لوني...")
    gradient_img = Image.new("RGB", (60, 20), "white")
    pixels = gradient_img.load()
    
    for x in range(60):
        for y in range(20):
            # تدرج من الأحمر إلى الأزرق
            red = int(255 * (1 - x / 59))
            blue = int(255 * (x / 59))
            green = int(255 * (y / 19))
            pixels[x, y] = (red, green, blue)
    
    gradient_img.save("examples/gradient_60x20.png")
    print("   ✅ تم حفظ: examples/gradient_60x20.png")
    
    print(f"\n🎉 تم إنشاء 5 صور تجريبية في مجلد examples/")
    print("يمكنك الآن اختبار التطبيق باستخدام هذه الصور!")

if __name__ == "__main__":
    create_test_images()
