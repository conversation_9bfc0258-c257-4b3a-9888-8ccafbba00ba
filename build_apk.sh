#!/bin/bash

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دالة طباعة الشعار
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    📱 بناء APK للأندرويد 📱                  ║"
    echo "║                  Pixel Map Converter                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# دالة التحقق من Python
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ Python غير مثبت${NC}"
        echo "يرجى تثبيت Python 3.7 أو أحدث"
        exit 1
    fi
    
    echo -e "${GRE<PERSON>}✅ Python متوفر: $($PYTHON_CMD --version)${NC}"
}

# دالة التحقق من buildozer
check_buildozer() {
    if command -v buildozer &> /dev/null; then
        echo -e "${GREEN}✅ Buildozer متوفر${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  Buildozer غير مثبت${NC}"
        echo "جاري تثبيت Buildozer..."
        $PYTHON_CMD -m pip install buildozer
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ تم تثبيت Buildozer${NC}"
            return 0
        else
            echo -e "${RED}❌ فشل في تثبيت Buildozer${NC}"
            return 1
        fi
    fi
}

# دالة التحقق من متطلبات Kivy
check_kivy_requirements() {
    echo -e "${YELLOW}🔍 التحقق من متطلبات Kivy...${NC}"
    
    if ! $PYTHON_CMD -c "import kivy, kivymd" &> /dev/null; then
        echo -e "${YELLOW}⚠️  متطلبات Kivy مفقودة. جاري التثبيت...${NC}"
        $PYTHON_CMD -m pip install kivy kivymd plyer
        
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ فشل في تثبيت متطلبات Kivy${NC}"
            return 1
        fi
    fi
    
    echo -e "${GREEN}✅ متطلبات Kivy متوفرة${NC}"
    return 0
}

# دالة التحقق من Android SDK
check_android_sdk() {
    echo -e "${YELLOW}🔍 التحقق من Android SDK...${NC}"
    
    if [ -z "$ANDROID_HOME" ] && [ -z "$ANDROID_SDK_ROOT" ]; then
        echo -e "${YELLOW}⚠️  متغيرات Android SDK غير مُعرفة${NC}"
        echo "يرجى تعيين ANDROID_HOME و ANDROID_SDK_ROOT"
        return 1
    fi
    
    echo -e "${GREEN}✅ Android SDK مُعرف${NC}"
    return 0
}

# دالة تحضير ملفات البناء
prepare_build_files() {
    echo -e "${YELLOW}📁 تحضير ملفات البناء...${NC}"
    
    if [ ! -f "android_app.py" ]; then
        echo -e "${RED}❌ ملف android_app.py غير موجود${NC}"
        return 1
    fi
    
    cp android_app.py main.py
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ تم تحضير الملفات${NC}"
        return 0
    else
        echo -e "${RED}❌ فشل في تحضير الملفات${NC}"
        return 1
    fi
}

# دالة بناء APK للاختبار
build_debug_apk() {
    echo -e "${YELLOW}🔨 بناء APK للاختبار...${NC}"
    echo "═══════════════════════════════════════"
    echo
    
    if ! prepare_build_files; then
        return 1
    fi
    
    echo -e "${BLUE}🔨 بدء بناء APK...${NC}"
    echo "هذا قد يستغرق عدة دقائق في المرة الأولى..."
    echo
    
    buildozer android debug
    
    if [ $? -eq 0 ]; then
        echo
        echo -e "${GREEN}✅ تم بناء APK بنجاح!${NC}"
        echo -e "${CYAN}📱 ملف APK موجود في مجلد bin/${NC}"
        ls -la bin/*.apk 2>/dev/null || echo "لم يتم العثور على ملف APK"
    else
        echo
        echo -e "${RED}❌ فشل في بناء APK${NC}"
        echo "راجع الأخطاء أعلاه"
    fi
}

# دالة بناء APK للإصدار النهائي
build_release_apk() {
    echo -e "${YELLOW}🚀 بناء APK للإصدار النهائي...${NC}"
    echo "═══════════════════════════════════════"
    echo
    
    echo -e "${YELLOW}⚠️  تحذير: بناء الإصدار النهائي يتطلب:${NC}"
    echo "   - Android SDK مثبت ومُعد"
    echo "   - متغيرات البيئة ANDROID_HOME و ANDROID_SDK_ROOT"
    echo "   - Java JDK 8"
    echo
    
    read -p "هل تريد المتابعة؟ (y/n): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        return 0
    fi
    
    if ! check_android_sdk; then
        echo -e "${RED}❌ يرجى إعداد Android SDK أولاً${NC}"
        return 1
    fi
    
    if ! prepare_build_files; then
        return 1
    fi
    
    echo -e "${BLUE}🚀 بدء بناء APK للإصدار النهائي...${NC}"
    buildozer android release
    
    if [ $? -eq 0 ]; then
        echo
        echo -e "${GREEN}✅ تم بناء APK النهائي بنجاح!${NC}"
        echo -e "${CYAN}📱 ملف APK موجود في مجلد bin/${NC}"
    else
        echo
        echo -e "${RED}❌ فشل في بناء APK النهائي${NC}"
    fi
}

# دالة تنظيف ملفات البناء
clean_build() {
    echo -e "${YELLOW}🧹 تنظيف ملفات البناء...${NC}"
    echo "═══════════════════════════════════════"
    echo
    
    if [ -d ".buildozer" ]; then
        echo -e "${BLUE}🗑️  حذف مجلد .buildozer...${NC}"
        rm -rf .buildozer
        echo -e "${GREEN}✅ تم حذف .buildozer${NC}"
    fi
    
    if [ -f "main.py" ] && [ ! -f "main_android.py" ]; then
        echo -e "${BLUE}🗑️  حذف main.py...${NC}"
        rm main.py
        echo -e "${GREEN}✅ تم حذف main.py${NC}"
    fi
    
    if [ -d "__pycache__" ]; then
        echo -e "${BLUE}🗑️  حذف __pycache__...${NC}"
        rm -rf __pycache__
        echo -e "${GREEN}✅ تم حذف __pycache__${NC}"
    fi
    
    echo
    echo -e "${GREEN}✅ تم التنظيف بنجاح${NC}"
}

# دالة تثبيت المتطلبات
install_requirements() {
    echo -e "${YELLOW}📦 تثبيت المتطلبات...${NC}"
    echo "═══════════════════════════════════════"
    echo
    
    echo -e "${BLUE}🔄 تحديث pip...${NC}"
    $PYTHON_CMD -m pip install --upgrade pip
    
    echo -e "${BLUE}🔄 تثبيت buildozer...${NC}"
    $PYTHON_CMD -m pip install buildozer
    
    echo -e "${BLUE}🔄 تثبيت Cython...${NC}"
    $PYTHON_CMD -m pip install cython
    
    echo -e "${BLUE}🔄 تثبيت متطلبات التطبيق...${NC}"
    $PYTHON_CMD -m pip install -r requirements.txt
    
    echo
    echo -e "${GREEN}✅ تم تثبيت جميع المتطلبات${NC}"
}

# دالة عرض معلومات البيئة
show_environment_info() {
    echo -e "${YELLOW}ℹ️  معلومات البيئة${NC}"
    echo "═══════════════════════════════════════"
    echo
    
    echo -e "${BLUE}🐍 Python:${NC}"
    $PYTHON_CMD --version
    
    echo
    echo -e "${BLUE}🔧 Buildozer:${NC}"
    buildozer version 2>/dev/null || echo "غير مثبت"
    
    echo
    echo -e "${BLUE}📱 Android SDK:${NC}"
    if [ -n "$ANDROID_HOME" ]; then
        echo "ANDROID_HOME: $ANDROID_HOME"
    else
        echo "ANDROID_HOME: غير مُعرف"
    fi
    
    if [ -n "$ANDROID_SDK_ROOT" ]; then
        echo "ANDROID_SDK_ROOT: $ANDROID_SDK_ROOT"
    else
        echo "ANDROID_SDK_ROOT: غير مُعرف"
    fi
    
    echo
    echo -e "${BLUE}☕ Java:${NC}"
    java -version 2>/dev/null || echo "غير مثبت"
    
    echo
    echo -e "${BLUE}📁 ملفات المشروع:${NC}"
    [ -f "android_app.py" ] && echo "✅ android_app.py" || echo "❌ android_app.py"
    [ -f "buildozer.spec" ] && echo "✅ buildozer.spec" || echo "❌ buildozer.spec"
    [ -d "src" ] && echo "✅ مجلد src" || echo "❌ مجلد src"
}

# دالة عرض القائمة
show_menu() {
    echo
    echo "═══════════════════════════════════════════════════════════════"
    echo "                         قائمة بناء APK"
    echo "═══════════════════════════════════════════════════════════════"
    echo
    echo "1. بناء APK للاختبار (Debug)"
    echo "2. بناء APK للإصدار النهائي (Release)"
    echo "3. تنظيف ملفات البناء"
    echo "4. تثبيت المتطلبات"
    echo "5. عرض معلومات البيئة"
    echo "6. خروج"
    echo
}

# الدالة الرئيسية
main() {
    print_banner
    check_python
    
    if ! check_buildozer; then
        exit 1
    fi
    
    if ! check_kivy_requirements; then
        exit 1
    fi
    
    while true; do
        show_menu
        read -p "اختر رقماً (1-6): " choice
        
        case $choice in
            1) build_debug_apk ;;
            2) build_release_apk ;;
            3) clean_build ;;
            4) install_requirements ;;
            5) show_environment_info ;;
            6) 
                echo
                echo -e "${GREEN}👋 شكراً لاستخدام Pixel Map Converter APK Builder!${NC}"
                echo
                echo -e "${CYAN}📖 للمزيد من المعلومات، راجع:${NC}"
                echo "   - APK_BUILD_GUIDE.md"
                echo "   - README.md"
                echo
                exit 0
                ;;
            *) 
                echo -e "${RED}❌ اختيار غير صحيح${NC}"
                ;;
        esac
        
        echo
        read -p "اضغط Enter للمتابعة..."
    done
}

# تشغيل التطبيق
main
