// Main JavaScript for Image Encryption Website
// متغيرات عامة
let currentUserKey = null;
let currentEncryptedMap = null;
let currentImageData = null;
let currentDecryptedImage = null;
let userFingerprint = null;

// نظام تحديد المفاتيح
let keyGenerationCount = 0;
let keyGenerationTimer = null;
const MAX_KEYS_PER_MINUTE = 2;

// عناصر DOM
const elements = {
    imageInput: document.getElementById('imageInput'),
    encryptedMapInput: document.getElementById('encryptedMapInput'),
    originalCanvas: document.getElementById('originalCanvas'),
    encryptedPreview: document.getElementById('encryptedPreview'),
    decryptedCanvas: document.getElementById('decryptedCanvas'),
    encryptionInfo: document.getElementById('encryptionInfo'),
    decryptionInfo: document.getElementById('decryptionInfo'),
    keyDisplay: document.getElementById('keyDisplay'),
    generatedKey: document.getElementById('generatedKey'),
    keyFingerprint: document.getElementById('keyFingerprint'),
    keyLength: document.getElementById('keyLength'),
    existingKeyInput: document.getElementById('existingKeyInput'),
    userKey: document.getElementById('userKey'),
    encryptBtn: document.getElementById('encryptBtn'),
    decryptBtn: document.getElementById('decryptBtn'),
    downloadEncryptedBtn: document.getElementById('downloadEncryptedBtn'),
    downloadDecryptedBtn: document.getElementById('downloadDecryptedBtn'),

    encryptionLevel: document.getElementById('encryptionLevel'),

};

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadUserData();
});

function initializeApp() {
    console.log('🚀 تهيئة تطبيق تشفير الصور...');

    // تحديث رسائل الترحيب
    updateWelcomeMessages();

    // تحميل المفتاح المحفوظ إن وجد
    loadSavedKey();

    console.log('✅ تم تهيئة التطبيق بنجاح');
}

function setupEventListeners() {
    // مستمعي أحداث رفع الملفات
    if (elements.imageInput) {
        elements.imageInput.addEventListener('change', handleImageSelect);
    }

    if (elements.encryptedMapInput) {
        elements.encryptedMapInput.addEventListener('change', handleEncryptedMapSelect);
    }

    // مستمع تغيير المفتاح
    if (elements.userKey) {
        elements.userKey.addEventListener('input', validateKeyInput);
    }

    // مستمعي التنقل السلس
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

function updateWelcomeMessages() {
    if (elements.encryptionInfo) {
        elements.encryptionInfo.textContent = `🔐 مرحباً بك في موقع تشفير الصور!

هذا الموقع يوفر:
• تشفير متقدم AES لحماية صورك
• مفاتيح خاصة بطول 50 حرف
• تخزين آمن في Firebase Cloud
• أمان عالي - لا أحد يمكنه فتح صورك بدون مفتاحك

ابدأ بإنشاء مفتاح خاص أو استخدام مفتاح موجود!`;
    }

    if (elements.decryptionInfo) {
        elements.decryptionInfo.textContent = `🔓 فك التشفير الآمن

لفك تشفير صورة:
1. اختر ملف الخريطة المشفرة (.json) أو من السحابة
2. تأكد من صحة مفتاحك الخاص
3. اضغط فك التشفير

تذكر: فقط مفتاحك الصحيح يمكنه فك التشفير!`;
    }
}

// إدارة المفاتيح
function generateUserKey(length = 50) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let key = '';
    for (let i = 0; i < length; i++) {
        key += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return key;
}

function generateFingerprint(key) {
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
        const char = key.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return Math.abs(hash).toString(16).substring(0, 8).toUpperCase();
}

// فحص تحديد المفاتيح
function canGenerateKey() {
    return keyGenerationCount < MAX_KEYS_PER_MINUTE;
}

function incrementKeyGeneration() {
    keyGenerationCount++;
    updateKeyCounter();

    // إعادة تعيين العداد بعد دقيقة
    if (keyGenerationTimer) {
        clearTimeout(keyGenerationTimer);
    }

    keyGenerationTimer = setTimeout(() => {
        keyGenerationCount = 0;
        updateKeyCounter();
        console.log('🔄 تم إعادة تعيين عداد المفاتيح');
    }, 60000); // دقيقة واحدة
}

function updateKeyCounter() {
    const keyCountElement = document.getElementById('keyCount');
    if (keyCountElement) {
        keyCountElement.textContent = keyGenerationCount;

        // تغيير اللون حسب العدد
        if (keyGenerationCount >= MAX_KEYS_PER_MINUTE) {
            keyCountElement.style.color = '#d32f2f';
        } else {
            keyCountElement.style.color = '#d84315';
        }
    }
}

function generateNewKey() {
    // فحص تحديد المفاتيح
    if (!canGenerateKey()) {
        alert(`⚠️ يمكنك إنشاء ${MAX_KEYS_PER_MINUTE} مفاتيح فقط في الدقيقة الواحدة.\nيرجى الانتظار قليلاً قبل إنشاء مفتاح جديد.`);
        return;
    }

    const key = generateUserKey(50);
    const fingerprint = generateFingerprint(key);

    // زيادة عداد المفاتيح
    incrementKeyGeneration();

    currentUserKey = key;
    userFingerprint = fingerprint;

    // عرض المفتاح
    if (elements.generatedKey) {
        elements.generatedKey.textContent = key;
    }

    if (elements.keyFingerprint) {
        elements.keyFingerprint.textContent = `بصمة: ${fingerprint}`;
    }

    if (elements.keyLength) {
        elements.keyLength.textContent = `الطول: ${key.length} حرف`;
    }

    if (elements.keyDisplay) {
        elements.keyDisplay.style.display = 'block';
    }

    if (elements.existingKeyInput) {
        elements.existingKeyInput.style.display = 'none';
    }

    // حفظ المفتاح محلياً
    saveKeyLocally(key, fingerprint);

    console.log('🔑 تم إنشاء مفتاح جديد:', fingerprint);

    // تحديث حالة الأزرار
    updateButtonStates();

    console.log('🔑 تم إنشاء مفتاح جديد:', fingerprint);
}

function loadExistingKey() {
    if (elements.existingKeyInput) {
        elements.existingKeyInput.style.display = 'block';
    }

    if (elements.keyDisplay) {
        elements.keyDisplay.style.display = 'none';
    }
}

function validateAndSetKey() {
    const key = elements.userKey?.value?.trim();

    if (!key) {
        alert('يرجى إدخال مفتاح');
        return;
    }

    if (key.length < 20) {
        alert('المفتاح قصير جداً (يجب أن يكون 20 حرف على الأقل)');
        return;
    }

    if (key.length > 100) {
        alert('المفتاح طويل جداً (يجب أن يكون أقل من 100 حرف)');
        return;
    }

    // التحقق من الأحرف المسموحة
    const allowedChars = /^[a-zA-Z0-9]+$/;
    if (!allowedChars.test(key)) {
        alert('المفتاح يحتوي على أحرف غير مسموحة (فقط أحرف وأرقام)');
        return;
    }

    // تعيين المفتاح
    currentUserKey = key;
    userFingerprint = generateFingerprint(key);

    // عرض معلومات المفتاح
    if (elements.generatedKey) {
        elements.generatedKey.textContent = key;
    }

    if (elements.keyFingerprint) {
        elements.keyFingerprint.textContent = `بصمة: ${userFingerprint}`;
    }

    if (elements.keyLength) {
        elements.keyLength.textContent = `الطول: ${key.length} حرف`;
    }

    if (elements.keyDisplay) {
        elements.keyDisplay.style.display = 'block';
    }

    if (elements.existingKeyInput) {
        elements.existingKeyInput.style.display = 'none';
    }

    // حفظ المفتاح محلياً
    saveKeyLocally(key, userFingerprint);

    // تحديث حالة الأزرار
    updateButtonStates();

    alert('✅ تم تأكيد المفتاح بنجاح!');
    console.log('🔑 تم تحميل مفتاح موجود:', userFingerprint);
}

function validateKeyInput() {
    const key = elements.userKey?.value?.trim();
    const isValid = key && key.length >= 20 && key.length <= 100 && /^[a-zA-Z0-9]+$/.test(key);

    // تغيير لون الحقل حسب الصحة
    if (elements.userKey) {
        elements.userKey.style.borderColor = isValid ? '#4CAF50' : '#F44336';
    }
}

function copyKey() {
    const keyText = elements.generatedKey?.textContent;
    if (keyText) {
        navigator.clipboard.writeText(keyText).then(() => {
            alert('تم نسخ المفتاح! احفظه في مكان آمن.');
        }).catch(() => {
            // fallback للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = keyText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('تم نسخ المفتاح! احفظه في مكان آمن.');
        });
    }
}

function saveKeyLocally(key, fingerprint) {
    try {
        const keyData = {
            key: key,
            fingerprint: fingerprint,
            savedAt: new Date().toISOString()
        };
        localStorage.setItem('imageEncryption_userKey', JSON.stringify(keyData));
    } catch (error) {
        console.warn('لا يمكن حفظ المفتاح محلياً:', error);
    }
}

function loadSavedKey() {
    try {
        const savedData = localStorage.getItem('imageEncryption_userKey');
        if (savedData) {
            const keyData = JSON.parse(savedData);
            currentUserKey = keyData.key;
            userFingerprint = keyData.fingerprint;

            // عرض المفتاح المحفوظ
            if (elements.generatedKey) {
                elements.generatedKey.textContent = keyData.key;
            }

            if (elements.keyFingerprint) {
                elements.keyFingerprint.textContent = `بصمة: ${keyData.fingerprint}`;
            }

            if (elements.keyLength) {
                elements.keyLength.textContent = `الطول: ${keyData.key.length} حرف`;
            }

            if (elements.keyDisplay) {
                elements.keyDisplay.style.display = 'block';
            }

            updateButtonStates();
            console.log('🔑 تم تحميل مفتاح محفوظ:', keyData.fingerprint);
        }
    } catch (error) {
        console.warn('لا يمكن تحميل المفتاح المحفوظ:', error);
    }
}

function updateButtonStates() {
    const hasKey = currentUserKey && currentUserKey.length >= 20;
    const hasImage = currentImageData !== null;
    const hasEncryptedMap = currentEncryptedMap !== null;

    // أزرار التشفير
    if (elements.encryptBtn) {
        elements.encryptBtn.disabled = !(hasKey && hasImage);
    }

    // أزرار فك التشفير
    if (elements.decryptBtn) {
        elements.decryptBtn.disabled = !(hasKey && hasEncryptedMap);
    }
}

// دوال مساعدة للتنقل
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// تحميل بيانات المستخدم
function loadUserData() {
    // تحميل إعدادات الوضع الداكن
    loadDarkModePreference();

    // تهيئة عداد المفاتيح
    updateKeyCounter();
}

// وظائف الوضع الداكن
function toggleDarkMode() {
    const body = document.body;
    const darkModeToggle = document.querySelector('.dark-mode-toggle');

    body.classList.toggle('dark-mode');

    // تحديث أيقونة الزر
    if (body.classList.contains('dark-mode')) {
        darkModeToggle.textContent = '☀️';
        localStorage.setItem('darkMode', 'enabled');
    } else {
        darkModeToggle.textContent = '🌙';
        localStorage.setItem('darkMode', 'disabled');
    }
}

function loadDarkModePreference() {
    const darkMode = localStorage.getItem('darkMode');
    const body = document.body;
    const darkModeToggle = document.querySelector('.dark-mode-toggle');

    // جعل الوضع الداكن افتراضي
    if (darkMode === null || darkMode === 'enabled') {
        body.classList.add('dark-mode');
        if (darkModeToggle) {
            darkModeToggle.textContent = '☀️';
        }
        localStorage.setItem('darkMode', 'enabled');
    } else {
        if (darkModeToggle) {
            darkModeToggle.textContent = '🌙';
        }
    }
}

// تحديث رسائل الترحيب للتشفير المحلي
function updateWelcomeMessages() {
    if (elements.encryptionInfo) {
        elements.encryptionInfo.textContent = `🔐 مرحباً بك في موقع تشفير الصور!

هذا الموقع يوفر:
• تشفير محلي آمن في متصفحك
• مفاتيح خاصة بطول 50 حرف
• لا يتم إرسال أي بيانات للخوادم
• أمان عالي - أنت تملك مفتاحك فقط

ابدأ بإنشاء مفتاح خاص أو استخدام مفتاح موجود!`;
    }

    if (elements.decryptionInfo) {
        elements.decryptionInfo.textContent = `🔓 فك التشفير المحلي

لفك تشفير صورة:
1. اختر ملف الخريطة المشفرة (.json)
2. تأكد من صحة مفتاحك الخاص
3. اضغط فك التشفير

تذكر: فقط مفتاحك الصحيح يمكنه فك التشفير!`;
    }
}

// وظائف تغيير اللغة
function toggleLanguage() {
    const currentLang = localStorage.getItem('language') || 'en';
    const newLang = currentLang === 'en' ? 'ar' : 'en';

    localStorage.setItem('language', newLang);
    updateLanguage(newLang);
}

function updateLanguage(lang) {
    const languageToggle = document.querySelector('.language-toggle');
    const body = document.body;

    if (lang === 'ar') {
        // تغيير إلى العربية
        body.setAttribute('dir', 'rtl');
        body.setAttribute('lang', 'ar');
        if (languageToggle) {
            languageToggle.textContent = '🌐 AR';
        }
        updateContentToArabic();
    } else {
        // تغيير إلى الإنجليزية
        body.setAttribute('dir', 'ltr');
        body.setAttribute('lang', 'en');
        if (languageToggle) {
            languageToggle.textContent = '🌐 EN';
        }
        updateContentToEnglish();
    }
}

function updateContentToEnglish() {
    // تحديث النصوص إلى الإنجليزية
    const translations = {
        // Header
        'الرئيسية': 'Home',
        'تشفير': 'Encrypt',
        'فك التشفير': 'Decrypt',
        '📄 تشفير PDF': '📄 Encrypt PDF',
        '📄 فك تشفير PDF': '📄 Decrypt PDF',
        'حول': 'About',

        // Hero Section
        '🔐 تشفير الصور': '🔐 Image Encryption',
        'حماية صورك بتشفير آمن': 'Protect your images with secure encryption',
        'مفاتيح خاصة 50 حرف': '50-character private keys',
        'تشفير متقدم AES': 'Advanced AES encryption',
        'تشفير محلي آمن': 'Secure local encryption',
        '🔐 ابدأ التشفير': '🔐 Start Encryption',
        '🔑 إنشاء مفتاح': '🔑 Generate Key',

        // Key Management
        '🔑 إدارة المفتاح الخاص': '🔑 Private Key Management',
        '🛡️ الأمان:': '🛡️ Security:',
        'مفتاح خاص 50 حرف': '50-character private key',
        'تشفير AES متقدم': 'Advanced AES encryption',
        'تخزين آمن': 'Secure storage',
        'حماية كاملة': 'Complete protection',
        '🔐 إنشاء مفتاح خاص جديد': '🔐 Generate New Private Key',
        '📥 استخدام مفتاح موجود': '📥 Use Existing Key',
        '⏱️ يمكنك إنشاء مفتاحين فقط في الدقيقة الواحدة': '⏱️ You can generate only 2 keys per minute',
        'المفاتيح المُنشأة:': 'Generated keys:',
        '🔑 مفتاحك الخاص:': '🔑 Your Private Key:',
        '📋 نسخ': '📋 Copy',
        '⚠️ احفظ هذا المفتاح في مكان آمن! لن تتمكن من استرجاع صورك المشفرة بدونه!': '⚠️ Save this key in a safe place! You won\'t be able to recover your encrypted images without it!',
        '📥 أدخل مفتاحك الموجود:': '📥 Enter your existing key:',
        'أدخل مفتاحك الخاص هنا (50 حرف)': 'Enter your private key here (50 characters)',
        '✅ تأكيد المفتاح': '✅ Confirm Key',

        // Encryption Section
        '🔒 تشفير صورة إلى خريطة آمنة': '🔒 Encrypt Image to Secure Map',
        'اختيار صورة للتشفير': 'Choose image to encrypt',
        'PNG, JPG, JPEG, BMP': 'PNG, JPG, JPEG, BMP',
        '🔐 مستوى التشفير:': '🔐 Encryption Level:',
        '🔒 قياسي (سريع)': '🔒 Standard (Fast)',
        '🛡️ عالي (موصى به)': '🛡️ High (Recommended)',
        '🔐 أقصى (أبطأ)': '🔐 Maximum (Slowest)',
        '🔐 تشفير وتحويل': '🔐 Encrypt & Convert',
        '💾 تحميل الخريطة المشفرة': '💾 Download Encrypted Map',
        'الصورة الأصلية': 'Original Image',
        'معاينة مشفرة': 'Encrypted Preview',
        'اختر صورة وتأكد من وجود مفتاحك لبدء التشفير...': 'Choose an image and ensure you have your key to start encryption...',

        // Decryption Section
        '🔓 فك تشفير خريطة إلى صورة': '🔓 Decrypt Map to Image',
        'اختيار خريطة مشفرة': 'Choose encrypted map',
        'ملفات JSON فقط': 'JSON files only',
        '🔓 فك التشفير وإعادة البناء': '🔓 Decrypt & Rebuild',
        '💾 تحميل الصورة': '💾 Download Image',
        'الصورة المُعادة': 'Restored Image',
        'اختر خريطة مشفرة وتأكد من صحة مفتاحك لفك التشفير...': 'Choose an encrypted map and ensure your key is correct for decryption...',

        // About Section
        '📖 حول الموقع': '📖 About the Website',
        '🔐 تشفير محلي آمن': '🔐 Secure Local Encryption',
        'جميع عمليات التشفير تتم في متصفحك مباشرة. لا يتم إرسال أي بيانات للخوادم.': 'All encryption operations happen directly in your browser. No data is sent to servers.',
        '🔑 مفاتيحك ملكك': '🔑 Your Keys Belong to You',
        'أنت الوحيد الذي يملك مفتاح التشفير. لا أحد يمكنه الوصول لصورك بدونه.': 'You are the only one who owns the encryption key. No one can access your images without it.',
        '💻 يعمل بدون إنترنت': '💻 Works Offline',
        'بعد تحميل الصفحة، يمكنك استخدام الموقع بدون اتصال إنترنت.': 'After loading the page, you can use the website without an internet connection.',
        '📧 الدعم والمساعدة': '📧 Support & Help',
        'هل تحتاج مساعدة أو لديك استفسار؟': 'Need help or have a question?',
        'نحن هنا لمساعدتك في أي وقت!': 'We\'re here to help you anytime!',

        // PDF Section
        '📄 تشفير ملفات PDF': '📄 PDF File Encryption',
        '📄 فك تشفير ملفات PDF': '📄 PDF File Decryption',
        'تشفير ملفات PDF بنفس مستوى الأمان المتقدم': 'Encrypt PDF files with the same advanced security level',
        'فك تشفير ملفات PDF واستعادة الملف الأصلي': 'Decrypt PDF files and restore the original file',
        '🔒 تشفير PDF': '🔒 Encrypt PDF',
        '🔓 فك تشفير PDF': '🔓 Decrypt PDF',
        'اختيار ملف PDF للتشفير': 'Choose PDF file to encrypt',
        'ملفات PDF فقط': 'PDF files only',
        '🔐 تشفير PDF': '🔐 Encrypt PDF',
        '💾 تحميل PDF المشفر': '💾 Download Encrypted PDF',
        'اختيار PDF مشفر': 'Choose encrypted PDF',
        '🔓 فك تشفير PDF': '🔓 Decrypt PDF',
        '💾 تحميل PDF الأصلي': '💾 Download Original PDF',
        'اختر ملف PDF وتأكد من وجود مفتاحك لبدء التشفير...': 'Choose a PDF file and ensure you have your key to start encryption...',
        'اختر ملف PDF مشفر وتأكد من صحة مفتاحك لفك التشفير...': 'Choose an encrypted PDF file and ensure your key is correct for decryption...',
        '🔐 تشفير متقدم': '🔐 Advanced Encryption',
        'نفس مستوى التشفير المستخدم للصور': 'Same encryption level used for images',
        '📄 دعم كامل': '📄 Full Support',
        'يدعم جميع أنواع ملفات PDF': 'Supports all types of PDF files',
        '🔒 أمان عالي': '🔒 High Security',
        'تشفير محلي بدون إرسال للخوادم': 'Local encryption without sending to servers',
        '🔓 فك تشفير آمن': '🔓 Secure Decryption',
        'استعادة ملفات PDF الأصلية بأمان': 'Safely restore original PDF files',
        '🔑 مفتاح واحد': '🔑 Single Key',
        'نفس المفتاح المستخدم في التشفير': 'Same key used for encryption',
        '📄 جودة كاملة': '📄 Full Quality',
        'استعادة PDF بنفس الجودة الأصلية': 'Restore PDF with original quality',

        // Footer
        'موقع تشفير الصور الآمن': 'Secure Image Encryption Website',
        'الخدمات': 'Services',
        'تشفير الصور': 'Image Encryption',
        'فك التشفير': 'Decryption',
        '📄 تشفير PDF': '📄 PDF Encryption',
        'حول الموقع': 'About Website',
        'الأمان': 'Security',
        'تشفير AES متقدم': 'Advanced AES encryption',
        'مفاتيح خاصة آمنة': 'Secure private keys',
        'تشفير محلي آمن': 'Secure local encryption'
    };

    // تطبيق الترجمات
    Object.keys(translations).forEach(arabicText => {
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
            if (element.children.length === 0 && element.textContent.trim() === arabicText) {
                element.textContent = translations[arabicText];
            }
        });
    });
}

function updateContentToArabic() {
    // تحديث النصوص إلى العربية (العكس)
    const translations = {
        // Header
        'Home': 'الرئيسية',
        'Encrypt': 'تشفير',
        'Decrypt': 'فك التشفير',
        '📄 Encrypt PDF': '📄 تشفير PDF',
        '📄 Decrypt PDF': '📄 فك تشفير PDF',
        'About': 'حول',

        // Hero Section
        '🔐 Image Encryption': '🔐 تشفير الصور',
        'Protect your images with secure encryption': 'حماية صورك بتشفير آمن',
        '50-character private keys': 'مفاتيح خاصة 50 حرف',
        'Advanced AES encryption': 'تشفير متقدم AES',
        'Secure local encryption': 'تشفير محلي آمن',
        '🔐 Start Encryption': '🔐 ابدأ التشفير',
        '🔑 Generate Key': '🔑 إنشاء مفتاح',

        // Key Management
        '🔑 Private Key Management': '🔑 إدارة المفتاح الخاص',
        '🛡️ Security:': '🛡️ الأمان:',
        '50-character private key': 'مفتاح خاص 50 حرف',
        'Advanced AES encryption': 'تشفير AES متقدم',
        'Secure storage': 'تخزين آمن',
        'Complete protection': 'حماية كاملة',
        '🔐 Generate New Private Key': '🔐 إنشاء مفتاح خاص جديد',
        '📥 Use Existing Key': '📥 استخدام مفتاح موجود',
        '⏱️ You can generate only 2 keys per minute': '⏱️ يمكنك إنشاء مفتاحين فقط في الدقيقة الواحدة',
        'Generated keys:': 'المفاتيح المُنشأة:',
        '🔑 Your Private Key:': '🔑 مفتاحك الخاص:',
        '📋 Copy': '📋 نسخ',
        '⚠️ Save this key in a safe place! You won\'t be able to recover your encrypted images without it!': '⚠️ احفظ هذا المفتاح في مكان آمن! لن تتمكن من استرجاع صورك المشفرة بدونه!',
        '📥 Enter your existing key:': '📥 أدخل مفتاحك الموجود:',
        'Enter your private key here (50 characters)': 'أدخل مفتاحك الخاص هنا (50 حرف)',
        '✅ Confirm Key': '✅ تأكيد المفتاح',

        // Encryption Section
        '🔒 Encrypt Image to Secure Map': '🔒 تشفير صورة إلى خريطة آمنة',
        'Choose image to encrypt': 'اختيار صورة للتشفير',
        'PNG, JPG, JPEG, BMP': 'PNG, JPG, JPEG, BMP',
        '🔐 Encryption Level:': '🔐 مستوى التشفير:',
        '🔒 Standard (Fast)': '🔒 قياسي (سريع)',
        '🛡️ High (Recommended)': '🛡️ عالي (موصى به)',
        '🔐 Maximum (Slowest)': '🔐 أقصى (أبطأ)',
        '🔐 Encrypt & Convert': '🔐 تشفير وتحويل',
        '💾 Download Encrypted Map': '💾 تحميل الخريطة المشفرة',
        'Original Image': 'الصورة الأصلية',
        'Encrypted Preview': 'معاينة مشفرة',
        'Choose an image and ensure you have your key to start encryption...': 'اختر صورة وتأكد من وجود مفتاحك لبدء التشفير...',

        // Decryption Section
        '🔓 Decrypt Map to Image': '🔓 فك تشفير خريطة إلى صورة',
        'Choose encrypted map': 'اختيار خريطة مشفرة',
        'JSON files only': 'ملفات JSON فقط',
        '🔓 Decrypt & Rebuild': '🔓 فك التشفير وإعادة البناء',
        '💾 Download Image': '💾 تحميل الصورة',
        'Restored Image': 'الصورة المُعادة',
        'Choose an encrypted map and ensure your key is correct for decryption...': 'اختر خريطة مشفرة وتأكد من صحة مفتاحك لفك التشفير...',

        // About Section
        '📖 About the Website': '📖 حول الموقع',
        '🔐 Secure Local Encryption': '🔐 تشفير محلي آمن',
        'All encryption operations happen directly in your browser. No data is sent to servers.': 'جميع عمليات التشفير تتم في متصفحك مباشرة. لا يتم إرسال أي بيانات للخوادم.',
        '🔑 Your Keys Belong to You': '🔑 مفاتيحك ملكك',
        'You are the only one who owns the encryption key. No one can access your images without it.': 'أنت الوحيد الذي يملك مفتاح التشفير. لا أحد يمكنه الوصول لصورك بدونه.',
        '💻 Works Offline': '💻 يعمل بدون إنترنت',
        'After loading the page, you can use the website without an internet connection.': 'بعد تحميل الصفحة، يمكنك استخدام الموقع بدون اتصال إنترنت.',
        '📧 Support & Help': '📧 الدعم والمساعدة',
        'Need help or have a question?': 'هل تحتاج مساعدة أو لديك استفسار؟',
        'We\'re here to help you anytime!': 'نحن هنا لمساعدتك في أي وقت!',

        // PDF Section
        '📄 PDF File Encryption': '📄 تشفير ملفات PDF',
        '📄 PDF File Decryption': '📄 فك تشفير ملفات PDF',
        'Encrypt PDF files with the same advanced security level': 'تشفير ملفات PDF بنفس مستوى الأمان المتقدم',
        'Decrypt PDF files and restore the original file': 'فك تشفير ملفات PDF واستعادة الملف الأصلي',
        '🔒 Encrypt PDF': '🔒 تشفير PDF',
        '🔓 Decrypt PDF': '🔓 فك تشفير PDF',
        'Choose PDF file to encrypt': 'اختيار ملف PDF للتشفير',
        'PDF files only': 'ملفات PDF فقط',
        '🔐 Encrypt PDF': '🔐 تشفير PDF',
        '💾 Download Encrypted PDF': '💾 تحميل PDF المشفر',
        'Choose encrypted PDF': 'اختيار PDF مشفر',
        '🔓 Decrypt PDF': '🔓 فك تشفير PDF',
        '💾 Download Original PDF': '💾 تحميل PDF الأصلي',
        'Choose a PDF file and ensure you have your key to start encryption...': 'اختر ملف PDF وتأكد من وجود مفتاحك لبدء التشفير...',
        'Choose an encrypted PDF file and ensure your key is correct for decryption...': 'اختر ملف PDF مشفر وتأكد من صحة مفتاحك لفك التشفير...',
        '🔐 Advanced Encryption': '🔐 تشفير متقدم',
        'Same encryption level used for images': 'نفس مستوى التشفير المستخدم للصور',
        '📄 Full Support': '📄 دعم كامل',
        'Supports all types of PDF files': 'يدعم جميع أنواع ملفات PDF',
        '🔒 High Security': '🔒 أمان عالي',
        'Local encryption without sending to servers': 'تشفير محلي بدون إرسال للخوادم',
        '🔓 Secure Decryption': '🔓 فك تشفير آمن',
        'Safely restore original PDF files': 'استعادة ملفات PDF الأصلية بأمان',
        '🔑 Single Key': '🔑 مفتاح واحد',
        'Same key used for encryption': 'نفس المفتاح المستخدم في التشفير',
        '📄 Full Quality': '📄 جودة كاملة',
        'Restore PDF with original quality': 'استعادة PDF بنفس الجودة الأصلية',

        // Footer
        'Secure Image Encryption Website': 'موقع تشفير الصور الآمن',
        'Services': 'الخدمات',
        'Image Encryption': 'تشفير الصور',
        'Decryption': 'فك التشفير',
        '📄 PDF Encryption': '📄 تشفير PDF',
        'About Website': 'حول الموقع',
        'Security': 'الأمان',
        'Advanced AES encryption': 'تشفير AES متقدم',
        'Secure private keys': 'مفاتيح خاصة آمنة',
        'Secure local encryption': 'تشفير محلي آمن'
    };

    // تطبيق الترجمات
    Object.keys(translations).forEach(englishText => {
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
            if (element.children.length === 0 && element.textContent.trim() === englishText) {
                element.textContent = translations[englishText];
            }
        });
    });
}

function loadLanguagePreference() {
    const language = localStorage.getItem('language') || 'en'; // الإنجليزية افتراضية
    updateLanguage(language);
}

// تحديث loadUserData لتشمل اللغة
function loadUserData() {
    // تحميل إعدادات الوضع الداكن
    loadDarkModePreference();

    // تحميل إعدادات اللغة
    loadLanguagePreference();

    // تهيئة عداد المفاتيح
    updateKeyCounter();
}

console.log('📱 تم تحميل main.js بنجاح');
