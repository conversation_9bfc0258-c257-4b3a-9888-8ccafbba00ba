// Main JavaScript for Image Encryption Website
// متغيرات عامة
let currentUserKey = null;
let currentEncryptedMap = null;
let currentImageData = null;
let currentDecryptedImage = null;
let userFingerprint = null;

// عناصر DOM
const elements = {
    imageInput: document.getElementById('imageInput'),
    encryptedMapInput: document.getElementById('encryptedMapInput'),
    originalCanvas: document.getElementById('originalCanvas'),
    encryptedPreview: document.getElementById('encryptedPreview'),
    decryptedCanvas: document.getElementById('decryptedCanvas'),
    encryptionInfo: document.getElementById('encryptionInfo'),
    decryptionInfo: document.getElementById('decryptionInfo'),
    keyDisplay: document.getElementById('keyDisplay'),
    generatedKey: document.getElementById('generatedKey'),
    keyFingerprint: document.getElementById('keyFingerprint'),
    keyLength: document.getElementById('keyLength'),
    existingKeyInput: document.getElementById('existingKeyInput'),
    userKey: document.getElementById('userKey'),
    encryptBtn: document.getElementById('encryptBtn'),
    decryptBtn: document.getElementById('decryptBtn'),
    downloadEncryptedBtn: document.getElementById('downloadEncryptedBtn'),
    downloadDecryptedBtn: document.getElementById('downloadDecryptedBtn'),
    saveToCloud: document.getElementById('saveToCloud'),
    encryptionLevel: document.getElementById('encryptionLevel'),
    filesGrid: document.getElementById('filesGrid'),
    filesCount: document.getElementById('filesCount'),
    cloudFilesList: document.getElementById('cloudFilesList')
};

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadUserData();
});

function initializeApp() {
    console.log('🚀 تهيئة تطبيق تشفير الصور...');
    
    // تحديث رسائل الترحيب
    updateWelcomeMessages();
    
    // تحميل المفتاح المحفوظ إن وجد
    loadSavedKey();
    
    // تسجيل زيارة الصفحة
    if (window.firebaseService) {
        window.firebaseService.logUsage('app-initialized');
    }
}

function setupEventListeners() {
    // مستمعي أحداث رفع الملفات
    if (elements.imageInput) {
        elements.imageInput.addEventListener('change', handleImageSelect);
    }
    
    if (elements.encryptedMapInput) {
        elements.encryptedMapInput.addEventListener('change', handleEncryptedMapSelect);
    }
    
    // مستمع تغيير المفتاح
    if (elements.userKey) {
        elements.userKey.addEventListener('input', validateKeyInput);
    }
    
    // مستمعي التنقل السلس
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

function updateWelcomeMessages() {
    if (elements.encryptionInfo) {
        elements.encryptionInfo.textContent = `🔐 مرحباً بك في موقع تشفير الصور!

هذا الموقع يوفر:
• تشفير متقدم AES لحماية صورك
• مفاتيح خاصة بطول 50 حرف
• تخزين آمن في Firebase Cloud
• أمان عالي - لا أحد يمكنه فتح صورك بدون مفتاحك

ابدأ بإنشاء مفتاح خاص أو استخدام مفتاح موجود!`;
    }

    if (elements.decryptionInfo) {
        elements.decryptionInfo.textContent = `🔓 فك التشفير الآمن

لفك تشفير صورة:
1. اختر ملف الخريطة المشفرة (.json) أو من السحابة
2. تأكد من صحة مفتاحك الخاص
3. اضغط فك التشفير

تذكر: فقط مفتاحك الصحيح يمكنه فك التشفير!`;
    }
}

// إدارة المفاتيح
function generateUserKey(length = 50) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let key = '';
    for (let i = 0; i < length; i++) {
        key += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return key;
}

function generateFingerprint(key) {
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
        const char = key.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return Math.abs(hash).toString(16).substring(0, 8).toUpperCase();
}

function generateNewKey() {
    const key = generateUserKey(50);
    const fingerprint = generateFingerprint(key);
    
    currentUserKey = key;
    userFingerprint = fingerprint;
    
    // عرض المفتاح
    if (elements.generatedKey) {
        elements.generatedKey.textContent = key;
    }
    
    if (elements.keyFingerprint) {
        elements.keyFingerprint.textContent = `بصمة: ${fingerprint}`;
    }
    
    if (elements.keyLength) {
        elements.keyLength.textContent = `الطول: ${key.length} حرف`;
    }
    
    if (elements.keyDisplay) {
        elements.keyDisplay.style.display = 'block';
    }
    
    if (elements.existingKeyInput) {
        elements.existingKeyInput.style.display = 'none';
    }
    
    // حفظ المفتاح محلياً
    saveKeyLocally(key, fingerprint);
    
    // تسجيل الحدث
    if (window.firebaseService) {
        window.firebaseService.logUsage('key-generated', {
            fingerprint: fingerprint,
            keyLength: key.length
        });
    }
    
    // تحديث حالة الأزرار
    updateButtonStates();
    
    console.log('🔑 تم إنشاء مفتاح جديد:', fingerprint);
}

function loadExistingKey() {
    if (elements.existingKeyInput) {
        elements.existingKeyInput.style.display = 'block';
    }
    
    if (elements.keyDisplay) {
        elements.keyDisplay.style.display = 'none';
    }
}

function validateAndSetKey() {
    const key = elements.userKey?.value?.trim();
    
    if (!key) {
        alert('يرجى إدخال مفتاح');
        return;
    }
    
    if (key.length < 20) {
        alert('المفتاح قصير جداً (يجب أن يكون 20 حرف على الأقل)');
        return;
    }
    
    if (key.length > 100) {
        alert('المفتاح طويل جداً (يجب أن يكون أقل من 100 حرف)');
        return;
    }
    
    // التحقق من الأحرف المسموحة
    const allowedChars = /^[a-zA-Z0-9]+$/;
    if (!allowedChars.test(key)) {
        alert('المفتاح يحتوي على أحرف غير مسموحة (فقط أحرف وأرقام)');
        return;
    }
    
    // تعيين المفتاح
    currentUserKey = key;
    userFingerprint = generateFingerprint(key);
    
    // عرض معلومات المفتاح
    if (elements.generatedKey) {
        elements.generatedKey.textContent = key;
    }
    
    if (elements.keyFingerprint) {
        elements.keyFingerprint.textContent = `بصمة: ${userFingerprint}`;
    }
    
    if (elements.keyLength) {
        elements.keyLength.textContent = `الطول: ${key.length} حرف`;
    }
    
    if (elements.keyDisplay) {
        elements.keyDisplay.style.display = 'block';
    }
    
    if (elements.existingKeyInput) {
        elements.existingKeyInput.style.display = 'none';
    }
    
    // حفظ المفتاح محلياً
    saveKeyLocally(key, userFingerprint);
    
    // تسجيل الحدث
    if (window.firebaseService) {
        window.firebaseService.logUsage('key-loaded', {
            fingerprint: userFingerprint,
            keyLength: key.length
        });
    }
    
    // تحديث حالة الأزرار
    updateButtonStates();
    
    // تحديث قائمة الملفات
    refreshFilesList();
    
    alert('✅ تم تأكيد المفتاح بنجاح!');
    console.log('🔑 تم تحميل مفتاح موجود:', userFingerprint);
}

function validateKeyInput() {
    const key = elements.userKey?.value?.trim();
    const isValid = key && key.length >= 20 && key.length <= 100 && /^[a-zA-Z0-9]+$/.test(key);
    
    // تغيير لون الحقل حسب الصحة
    if (elements.userKey) {
        elements.userKey.style.borderColor = isValid ? '#4CAF50' : '#F44336';
    }
}

function copyKey() {
    const keyText = elements.generatedKey?.textContent;
    if (keyText) {
        navigator.clipboard.writeText(keyText).then(() => {
            alert('تم نسخ المفتاح! احفظه في مكان آمن.');
        }).catch(() => {
            // fallback للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = keyText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('تم نسخ المفتاح! احفظه في مكان آمن.');
        });
    }
}

function saveKeyLocally(key, fingerprint) {
    try {
        const keyData = {
            key: key,
            fingerprint: fingerprint,
            savedAt: new Date().toISOString()
        };
        localStorage.setItem('imageEncryption_userKey', JSON.stringify(keyData));
    } catch (error) {
        console.warn('لا يمكن حفظ المفتاح محلياً:', error);
    }
}

function loadSavedKey() {
    try {
        const savedData = localStorage.getItem('imageEncryption_userKey');
        if (savedData) {
            const keyData = JSON.parse(savedData);
            currentUserKey = keyData.key;
            userFingerprint = keyData.fingerprint;
            
            // عرض المفتاح المحفوظ
            if (elements.generatedKey) {
                elements.generatedKey.textContent = keyData.key;
            }
            
            if (elements.keyFingerprint) {
                elements.keyFingerprint.textContent = `بصمة: ${keyData.fingerprint}`;
            }
            
            if (elements.keyLength) {
                elements.keyLength.textContent = `الطول: ${keyData.key.length} حرف`;
            }
            
            if (elements.keyDisplay) {
                elements.keyDisplay.style.display = 'block';
            }
            
            updateButtonStates();
            console.log('🔑 تم تحميل مفتاح محفوظ:', keyData.fingerprint);
        }
    } catch (error) {
        console.warn('لا يمكن تحميل المفتاح المحفوظ:', error);
    }
}

function updateButtonStates() {
    const hasKey = currentUserKey && currentUserKey.length >= 20;
    const hasImage = currentImageData !== null;
    const hasEncryptedMap = currentEncryptedMap !== null;
    
    // أزرار التشفير
    if (elements.encryptBtn) {
        elements.encryptBtn.disabled = !(hasKey && hasImage);
    }
    
    // أزرار فك التشفير
    if (elements.decryptBtn) {
        elements.decryptBtn.disabled = !(hasKey && hasEncryptedMap);
    }
}

// دوال مساعدة للتنقل
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function switchTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إظهار التبويب المحدد
    const targetTab = document.getElementById(tabName + 'Tab');
    if (targetTab) {
        targetTab.classList.add('active');
    }
    
    // تفعيل الزر المحدد
    event.target.classList.add('active');
    
    // تحميل الملفات إذا كان التبويب السحابي
    if (tabName === 'cloud') {
        loadCloudFiles();
    }
}

// تحميل بيانات المستخدم
function loadUserData() {
    if (userFingerprint) {
        refreshFilesList();
    }
}

console.log('📱 تم تحميل main.js بنجاح');
