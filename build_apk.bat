@echo off
chcp 65001 >nul
title Pixel Map Converter - APK Builder

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    📱 بناء APK للأندرويد 📱                  ║
echo ║                  Pixel Map Converter                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير مضاف إلى PATH
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM التحقق من buildozer
buildozer version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Buildozer غير مثبت
    echo.
    echo جاري تثبيت Buildozer...
    python -m pip install buildozer
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Buildozer
        echo يرجى تثبيته يدوياً: pip install buildozer
        pause
        exit /b 1
    )
)

echo ✅ Buildozer متوفر
echo.

REM التحقق من متطلبات Kivy
echo 🔍 التحقق من متطلبات Kivy...
python -c "import kivy, kivymd" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  متطلبات Kivy مفقودة. جاري التثبيت...
    python -m pip install kivy kivymd plyer
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت متطلبات Kivy
        pause
        exit /b 1
    )
)

echo ✅ متطلبات Kivy متوفرة
echo.

REM عرض القائمة
:menu
echo ═══════════════════════════════════════════════════════════════
echo                         قائمة بناء APK
echo ═══════════════════════════════════════════════════════════════
echo.
echo 1. بناء APK للاختبار (Debug)
echo 2. بناء APK للإصدار النهائي (Release)
echo 3. تنظيف ملفات البناء
echo 4. تثبيت المتطلبات
echo 5. عرض معلومات البيئة
echo 6. خروج
echo.
set /p choice="اختر رقماً (1-6): "

if "%choice%"=="1" goto build_debug
if "%choice%"=="2" goto build_release
if "%choice%"=="3" goto clean_build
if "%choice%"=="4" goto install_requirements
if "%choice%"=="5" goto show_info
if "%choice%"=="6" goto exit
echo ❌ اختيار غير صحيح
goto menu

:build_debug
echo.
echo 🔨 بناء APK للاختبار...
echo ═══════════════════════════════════════
echo.

REM تحضير الملفات
if not exist "android_app.py" (
    echo ❌ ملف android_app.py غير موجود
    pause
    goto menu
)

echo 📁 تحضير ملفات البناء...
copy /Y android_app.py main.py >nul
if %errorlevel% neq 0 (
    echo ❌ فشل في نسخ الملفات
    pause
    goto menu
)

echo ✅ تم تحضير الملفات
echo.

echo 🔨 بدء بناء APK...
echo هذا قد يستغرق عدة دقائق في المرة الأولى...
echo.

buildozer android debug
if %errorlevel% equ 0 (
    echo.
    echo ✅ تم بناء APK بنجاح!
    echo 📱 ملف APK موجود في مجلد bin/
    dir /b bin\*.apk 2>nul
) else (
    echo.
    echo ❌ فشل في بناء APK
    echo راجع الأخطاء أعلاه
)

echo.
pause
goto menu

:build_release
echo.
echo 🚀 بناء APK للإصدار النهائي...
echo ═══════════════════════════════════════
echo.

echo ⚠️  تحذير: بناء الإصدار النهائي يتطلب:
echo    - Android SDK مثبت ومُعد
echo    - متغيرات البيئة ANDROID_HOME و ANDROID_SDK_ROOT
echo    - Java JDK 8
echo.
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i not "%confirm%"=="y" goto menu

REM تحضير الملفات
copy /Y android_app.py main.py >nul

echo 🚀 بدء بناء APK للإصدار النهائي...
buildozer android release

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم بناء APK النهائي بنجاح!
    echo 📱 ملف APK موجود في مجلد bin/
) else (
    echo.
    echo ❌ فشل في بناء APK النهائي
)

echo.
pause
goto menu

:clean_build
echo.
echo 🧹 تنظيف ملفات البناء...
echo ═══════════════════════════════════════
echo.

if exist ".buildozer" (
    echo 🗑️  حذف مجلد .buildozer...
    rmdir /s /q .buildozer
    echo ✅ تم حذف .buildozer
)

if exist "main.py" (
    if not exist "main_android.py" (
        echo 🗑️  حذف main.py...
        del main.py
        echo ✅ تم حذف main.py
    )
)

if exist "__pycache__" (
    echo 🗑️  حذف __pycache__...
    rmdir /s /q __pycache__
    echo ✅ تم حذف __pycache__
)

echo.
echo ✅ تم التنظيف بنجاح
pause
goto menu

:install_requirements
echo.
echo 📦 تثبيت المتطلبات...
echo ═══════════════════════════════════════
echo.

echo 🔄 تحديث pip...
python -m pip install --upgrade pip

echo 🔄 تثبيت buildozer...
python -m pip install buildozer

echo 🔄 تثبيت Cython...
python -m pip install cython

echo 🔄 تثبيت متطلبات التطبيق...
python -m pip install -r requirements.txt

echo.
echo ✅ تم تثبيت جميع المتطلبات
pause
goto menu

:show_info
echo.
echo ℹ️  معلومات البيئة
echo ═══════════════════════════════════════
echo.

echo 🐍 Python:
python --version

echo.
echo 🔧 Buildozer:
buildozer version 2>nul || echo غير مثبت

echo.
echo 📱 Android SDK:
if defined ANDROID_HOME (
    echo ANDROID_HOME: %ANDROID_HOME%
) else (
    echo ANDROID_HOME: غير مُعرف
)

if defined ANDROID_SDK_ROOT (
    echo ANDROID_SDK_ROOT: %ANDROID_SDK_ROOT%
) else (
    echo ANDROID_SDK_ROOT: غير مُعرف
)

echo.
echo ☕ Java:
java -version 2>nul || echo غير مثبت

echo.
echo 📁 ملفات المشروع:
if exist "android_app.py" (
    echo ✅ android_app.py
) else (
    echo ❌ android_app.py
)

if exist "buildozer.spec" (
    echo ✅ buildozer.spec
) else (
    echo ❌ buildozer.spec
)

if exist "src" (
    echo ✅ مجلد src
) else (
    echo ❌ مجلد src
)

echo.
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام Pixel Map Converter APK Builder!
echo.
echo 📖 للمزيد من المعلومات، راجع:
echo    - APK_BUILD_GUIDE.md
echo    - README.md
echo.
pause
exit /b 0
