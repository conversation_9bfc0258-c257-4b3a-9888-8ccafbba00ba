# 🔐 Image Encryption Website - Firebase Deployment

## 🌐 الموقع المباشر
**https://imageencryption.com**

## 🎯 نظرة عامة
موقع تشفير الصور الآمن مع Firebase - يوفر تشفير متقدم للصور وتحويلها إلى خرائط بكسلات مشفرة.

## 🚀 المميزات
- 🔐 **تشفير متقدم** باستخدام AES
- 🔑 **مفاتيح خاصة** بطول 50 حرف
- ☁️ **تخزين سحابي** آمن في Firebase
- 📱 **واجهة متجاوبة** تعمل على جميع الأجهزة
- 🌍 **دعم عربي** كامل
- 📊 **إحصائيات الاستخدام** مع Firebase Analytics

## 🛠️ التقنيات المستخدمة
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Firebase (Hosting, Firestore, Storage, Analytics)
- **التشفير**: Web Crypto API, AES-256
- **التصميم**: CSS Grid, Flexbox, Responsive Design

## 📁 هيكل المشروع
```
image-encryption-website/
├── 🔧 إعدادات Firebase
│   ├── firebase.json              # إعدادات Firebase
│   ├── .firebaserc               # مشاريع Firebase
│   └── package.json              # إعدادات NPM
│
├── 🌐 الملفات العامة
│   └── public/
│       ├── index.html            # الصفحة الرئيسية
│       ├── firebase-config.js    # إعدادات Firebase
│       └── scripts/
│           ├── main.js           # الكود الرئيسي
│           ├── encryption.js     # وظائف التشفير
│           └── ui.js            # واجهة المستخدم
│
├── 📖 التوثيق
│   ├── README_FIREBASE.md        # هذا الملف
│   ├── DEPLOYMENT_GUIDE.md       # دليل النشر
│   └── API_DOCS.md              # توثيق API
│
└── 🔒 الأمان
    ├── storage.rules             # قواعد Firebase Storage
    ├── firestore.rules          # قواعد Firestore
    └── firestore.indexes.json   # فهارس Firestore
```

## 🚀 التثبيت والتشغيل

### **1. متطلبات النظام:**
```bash
# Node.js (الإصدار 14 أو أحدث)
node --version

# npm (الإصدار 6 أو أحدث)
npm --version

# Firebase CLI
npm install -g firebase-tools
```

### **2. إعداد المشروع:**
```bash
# استنساخ المشروع
git clone https://github.com/imageencryption/website.git
cd image-encryption-website

# تثبيت التبعيات
npm install

# تسجيل الدخول إلى Firebase
firebase login

# تهيئة Firebase (إذا لم يكن مُعد مسبقاً)
firebase init
```

### **3. التشغيل المحلي:**
```bash
# تشغيل الخادم المحلي
npm run dev
# أو
firebase serve --port 3000

# فتح المتصفح على
# http://localhost:3000
```

### **4. النشر إلى Firebase:**
```bash
# بناء المشروع
npm run build

# نشر إلى Firebase Hosting
npm run deploy
# أو
firebase deploy

# نشر قواعد البيانات فقط
firebase deploy --only firestore:rules

# نشر قواعد التخزين فقط
firebase deploy --only storage
```

## ⚙️ إعدادات Firebase

### **Firebase Configuration:**
```javascript
const firebaseConfig = {
  apiKey: "AIzaSyBdYEr2epZPoYc3DtG-aWFQlCZv8yuIzQ4",
  authDomain: "mage-41f51.firebaseapp.com",
  projectId: "mage-41f51",
  storageBucket: "mage-41f51.firebasestorage.app",
  messagingSenderId: "949188030661",
  appId: "1:949188030661:web:d426ce91c9763e8cea40ac",
  measurementId: "G-J12L13XPHZ"
};
```

### **الخدمات المستخدمة:**
- **Hosting**: استضافة الموقع
- **Firestore**: قاعدة بيانات للملفات المشفرة
- **Storage**: تخزين الملفات المشفرة
- **Analytics**: إحصائيات الاستخدام

## 🔒 الأمان والخصوصية

### **قواعد Firestore:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح بالقراءة والكتابة للملفات المشفرة
    match /encrypted-files/{document} {
      allow read, write: if request.auth != null 
        || resource.data.userFingerprint == request.query.userFingerprint;
    }
    
    // السماح بالكتابة فقط لإحصائيات الاستخدام
    match /usage-stats/{document} {
      allow write: if true;
      allow read: if false;
    }
  }
}
```

### **قواعد Storage:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // السماح برفع وتحميل الملفات المشفرة
    match /encrypted-maps/{userFingerprint}/{fileName} {
      allow read, write: if true;
    }
  }
}
```

## 📊 مراقبة الأداء

### **Firebase Analytics:**
- تتبع زيارات الصفحات
- إحصائيات التشفير وفك التشفير
- معدلات الاستخدام
- أخطاء النظام

### **مقاييس الأداء:**
- سرعة تحميل الصفحة
- معدل الارتداد
- وقت البقاء في الموقع
- معدل التحويل

## 🌍 ربط الدومين المخصص

### **إعداد imageencryption.com:**
```bash
# إضافة الدومين المخصص
firebase hosting:channel:deploy live --only hosting

# ربط الدومين
# 1. اذهب إلى Firebase Console
# 2. Hosting > Add custom domain
# 3. أدخل: imageencryption.com
# 4. اتبع التعليمات لإعداد DNS
```

### **إعدادات DNS:**
```
Type: A
Name: @
Value: *************
TTL: 3600

Type: A  
Name: @
Value: **************
TTL: 3600

Type: CNAME
Name: www
Value: imageencryption.com
TTL: 3600
```

## 🔧 أوامر مفيدة

### **Firebase CLI:**
```bash
# عرض المشاريع
firebase projects:list

# تبديل المشروع
firebase use mage-41f51

# عرض حالة النشر
firebase hosting:channel:list

# عرض السجلات
firebase functions:log

# نسخ احتياطية
firebase firestore:export gs://mage-41f51.appspot.com/backups
```

### **تطوير محلي:**
```bash
# تشغيل محاكي Firebase
firebase emulators:start

# تشغيل محاكي Firestore فقط
firebase emulators:start --only firestore

# تشغيل محاكي Storage فقط
firebase emulators:start --only storage
```

## 🐛 استكشاف الأخطاء

### **مشاكل شائعة:**

**1. خطأ في التشفير:**
```javascript
// التحقق من صحة المفتاح
if (!key || key.length < 20) {
  throw new Error('مفتاح غير صحيح');
}
```

**2. فشل رفع الملف:**
```javascript
// التحقق من صلاحيات Storage
const result = await uploadBytes(storageRef, file);
if (!result) {
  console.error('فشل في رفع الملف');
}
```

**3. مشاكل الاتصال:**
```javascript
// التحقق من حالة الشبكة
if (!navigator.onLine) {
  alert('لا يوجد اتصال بالإنترنت');
}
```

## 📈 تحسين الأداء

### **تحسينات مقترحة:**
- ضغط الصور قبل التشفير
- تخزين مؤقت للملفات المستخدمة كثيراً
- تحميل تدريجي للمكونات
- تحسين استعلامات Firestore

### **مراقبة الأداء:**
```javascript
// قياس وقت التشفير
console.time('encryption');
await encryptImage();
console.timeEnd('encryption');
```

## 🤝 المساهمة

### **كيفية المساهمة:**
1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى الفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📞 الدعم والتواصل

- **الموقع**: https://imageencryption.com
- **البريد الإلكتروني**: <EMAIL>
- **GitHub**: https://github.com/imageencryption/website
- **التوثيق**: https://docs.imageencryption.com

## 📄 الترخيص
هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

---

**🔐 موقع تشفير الصور الآمن - مدعوم بـ Firebase 🚀**
